/* ------------------------------------------------------------------------------
 *
 *  # Components
 *
 *  Components import. Ordering matters. See _config.scss for more options
 *
 * ---------------------------------------------------------------------------- */


// Core
// ------------------------------

// Import custom template config
@import "../../../../config";
@import "../../../../shared/utils/ll-functions";
@import "../../../../shared/utils/ll-mixins";
@import "../../../../themes/default/colors/palette";

// Core variables and mixins
@import "../../../../_bootstrap/functions";
@import "../../../../_bootstrap/variables";
@import "../../../../_bootstrap/mixins";

// Import template's variables
@import "../variables/variables-core";
@import "../variables/variables-custom";
@import "../../../../themes/default/bootstrap_limitless/mixins";

// FOUC helpers
@import "../../../../shared/utils/fouc";


// Page loader
// ------------------------------

// Pace page loader
@import "../../../../shared/pace/theme-default";

// Pace.js demo, remove in real project
@import "../../../../shared/pace/pace-demo";


// Forms
// ------------------------------

// Uniform
@import "../../../../themes/default/components/forms/uniform";

// Switchery
@import "../../../../themes/default/components/forms/switchery";

// BS switch
@import "../../../../themes/default/components/forms/bootstrap-switch";


// Select2
@import "../../../../themes/default/components/forms/select2";

// Multiselect
@import "../../../../themes/default/components/forms/multiselect";


// Passy
@import "../../../../themes/default/components/forms/passy";

// Typeahead
@import "../../../../themes/default/components/forms/typeahead";

// Validation
@import "../../../../themes/default/components/forms/validation";

// Floating labels
@import "../../../../themes/default/components/forms/floating-labels";

// Alpaca
@import "../../../../themes/default/components/forms/alpaca";


// Tokenfield
@import "../../../../themes/default/components/forms/tokenfield";

// Tags input
@import "../../../../themes/default/components/forms/tags-input";


// Touchspin spinners
@import "../../../../themes/default/components/forms/touchspin";

// Dual listboxes
@import "../../../../themes/default/components/forms/dual-listbox";


// Wizard
@import "../../../../themes/default/components/forms/wizard";


// Text editors
// ------------------------------

// Summernote
@import "../../../../themes/default/components/forms/editors/summernote";

// Trumbowyg
@import "../../../../themes/default/components/forms/editors/trumbowyg";

// Ace
@import "../../../../themes/default/components/forms/editors/ace";


// Pickers
// ------------------------------

// Daterange picker
@import "../../../../themes/default/components/pickers/daterange";

// Pickadate base
@import "../../../../themes/default/components/pickers/pickadate/base";

// Pickadate date picker
@import "../../../../themes/default/components/pickers/pickadate/date";

// Pickadate time picker
@import "../../../../themes/default/components/pickers/pickadate/time";

// Anytime picker
@import "../../../../themes/default/components/pickers/anytime";

// Color picker
@import "../../../../themes/default/components/pickers/spectrum";


// File uploaders
// ------------------------------

// Plupload
@import "../../../../themes/default/components/forms/uploaders/plupload";

// File input
@import "../../../../themes/default/components/forms/uploaders/file-input";

// Dropzone
@import "../../../../themes/default/components/forms/uploaders/dropzone";


// Notifications
// ------------------------------

// Noty notifications
@import "../../../../themes/default/components/notifications/noty";

// PNotify notifications
@import "../../../../themes/default/components/notifications/pnotify";

// jGrowl notifications
@import "../../../../themes/default/components/notifications/jgrowl";

// Sweet Alerts
@import "../../../../themes/default/components/notifications/sweet-alerts";


// Sliders
// ------------------------------

// Slider pips
@import "../../../../themes/default/components/sliders/slider-pips";

// NoUI slider
@import "../../../../themes/default/components/sliders/noui-slider";

// Ion range slider
@import "../../../../themes/default/components/sliders/ion-range-slider";


// jQuery UI components
// ------------------------------

// Interactions
@import "../../../../themes/default/components/jquery_ui/interactions";

// Widgets
@import "../../../../themes/default/components/jquery_ui/widgets";


// UI components
// ------------------------------

// Prism - syntax highlighter
@import "../../../../themes/default/components/ui/prism";

// Slinky - multi level drilldown menu
@import "../../../../themes/default/components/ui/slinky";

// Sticky kit
@import "../../../../themes/default/components/ui/sticky";

// Headroom - hhide navbar on scroll
@import "../../../../themes/default/components/ui/headroom";

// Dragula
@import "../../../../themes/default/components/ui/dragula";

// Perfect scrollbar
@import "../../../../themes/default/components/ui/perfect-scrollbar";


// Misc components
// ------------------------------

// [Material] Floating action buttons
@import "../../../../themes/default/components/misc/fab";

// Fancytree
@import "../../../../themes/default/components/misc/fancytree";

// Progress buttons
@import "../../../../themes/default/components/misc/progress-buttons";

// Full calendar
@import "../../../../themes/default/components/misc/fullcalendar";

// Image cropper
@import "../../../../themes/default/components/misc/image-cropper";

// Lightbox plugin
@import "../../../../themes/default/components/misc/fancybox";


// Tables
// ------------------------------

// Footable - responsive table tools
@import "../../../../themes/default/components/tables/footable";


// Handsontable
// ------------------------------

// Handsontable - excel-like spreadsheet for apps
@import "../../../../themes/default/components/tables/handsontable";


// Datatables
// ------------------------------

// Core
@import "../../../../themes/default/components/tables/datatables/datatables";

// Datatables - columns reorder
@import "../../../../themes/default/components/tables/datatables/datatable-columns-reorder";

// Datatables - rows reorder
@import "../../../../themes/default/components/tables/datatables/datatable-rows-reorder";

// Datatables - fixed columns
@import "../../../../themes/default/components/tables/datatables/datatable-fixed-columns";

// Datatables - fixed header
@import "../../../../themes/default/components/tables/datatables/datatable-fixed-header";

// Datatables - autofill
@import "../../../../themes/default/components/tables/datatables/datatable-autofill";

// Datatables - select
@import "../../../../themes/default/components/tables/datatables/datatable-select";

// Datatables - buttons
@import "../../../../themes/default/components/tables/datatables/datatable-buttons";

// Datatables - key table
@import "../../../../themes/default/components/tables/datatables/datatable-keytable";

// Datatables - scroller
@import "../../../../themes/default/components/tables/datatables/datatable-scroller";

// Datatables - responsive
@import "../../../../themes/default/components/tables/datatables/datatable-responsive";


// Maps
// ------------------------------

// Google Maps
@import "../../../../shared/maps/google-maps";

// jVectorMap
@import "../../../../shared/maps/jvectormap";


// Charts
// ------------------------------

// Charts base
@import "../../../../shared/charts/charts";

// C3 chart library
@import "../../../../shared/charts/c3";

// D3 chart library
@import "../../../../shared/charts/d3";


// Page kits
// ------------------------------

// ECommerce
@import "../../../../shared/pages/ecommerce";

// Blog
@import "../../../../shared/pages/blog";

// Task manager
@import "../../../../shared/pages/task-manager";

// Inbox
@import "../../../../shared/pages/inbox";

// Profile
@import "../../../../shared/pages/profile";

// Login
@import "../../../../shared/pages/login";

// Timelines
@import "../../../../shared/pages/timelines";

// Chats
@import "../../../../shared/pages/chats";

// Error page
@import "../../../../shared/pages/error";


// Other components
// ------------------------------

// Heading elements
@import "../../../../themes/default/components/ui/header-elements";

// Helpers
@import "../../../../shared/utils/helpers";

// Plugins demo, remove in real project
@import "../../../../themes/default/components/demo";
