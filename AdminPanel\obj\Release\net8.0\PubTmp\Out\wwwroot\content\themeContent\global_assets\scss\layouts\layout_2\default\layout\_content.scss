/* ------------------------------------------------------------------------------
 *
 *  # Main content layout
 *
 *  Styles for main structure of content area
 *
 * ---------------------------------------------------------------------------- */

// Make sure document has 100% height
html {
    display: flex;
    flex-direction: column;
}

// Document body
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    flex: 1;
}

// Main container
.page-content {
    display: flex;
    flex-grow: 1;
}

// Content wrapper
.content-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: auto;
}

// Main content container
.content {
    padding: $content-container-padding-y $content-container-padding-x;
    flex-grow: 1;
    @include clearfix;
}
