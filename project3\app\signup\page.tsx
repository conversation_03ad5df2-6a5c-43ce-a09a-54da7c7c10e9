'use client';

import { useEffect, useState } from 'react';
import PhoneVerification from '@/components/auth/phone-verification';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, CheckCircle2, Smartphone, Lock, User, Mail, RefreshCw, Home, MapPin, Building2, Eye, EyeOff } from 'lucide-react';

type FormStep = 'phone' | 'verification' | 'details';

export default function RegistrationPage() {
  const [formStep, setFormStep] = useState<FormStep>('phone');
  const [phone, setPhone] = useState('964');
  const [userCountry, setUserCountry] = useState('iq');
  const [verificationCode, setVerificationCode] = useState('');
  const [confirmationResult, setConfirmationResult] = useState<any>(null);
  const [resendTimer, setResendTimer] = useState(0);
  const [verificationTimer, setVerificationTimer] = useState(0);
  const [verificationExpired, setVerificationExpired] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [userDetails, setUserDetails] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    mobileNo: phone,
    cityId: '-999',
    stateProvinceId: '-999',
    countryId: '1'
  });

  useEffect(() => {
    fetch('https://ipapi.co/json/')
      .then(res => res.json())
      .then(data => {
        if (data.country_code) {
          setUserCountry(data.country_code.toLowerCase());
          setPhone(data.country_calling_code.replace('+', ''));
        }
      })
      .catch(() => {
        setUserCountry('iq');
        setPhone('964');
      });
  }, []);

  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimer]);

  useEffect(() => {
    if (verificationTimer > 0) {
      const timer = setTimeout(() => setVerificationTimer(verificationTimer - 1), 1000);
      return () => clearTimeout(timer);
    } else if (verificationTimer === 0 && formStep === 'verification' && !verificationExpired) {
      // Timer expired, show SweetAlert
      setVerificationExpired(true);
      showVerificationExpiredAlert();
    }
  }, [verificationTimer, formStep, verificationExpired]);

  const showVerificationExpiredAlert = async () => {
    const Swal = (await import('sweetalert2')).default;
    
    const result = await Swal.fire({
      title: 'Verification Expired!',
      text: 'Your verification code has expired. Please request a new code.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Get New Code',
      cancelButtonText: 'Cancel',
      confirmButtonColor: '#f59e0b',
      cancelButtonColor: '#6b7280',
      allowOutsideClick: false,
      allowEscapeKey: false
    });
    
    if (result.isConfirmed) {
      // Reset verification state and go back to phone step
      setFormStep('phone');
      setVerificationCode('');
      setVerificationTimer(0);
      setVerificationExpired(false);
      setError('');
    } else {
      // User cancelled, go back to phone step anyway
      setFormStep('phone');
      setVerificationCode('');
      setVerificationTimer(0);
      setVerificationExpired(false);
      setError('');
    }
  };

  const startResendTimer = () => {
    setResendTimer(180); // 3 minutes
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Enhanced phone number validation
    if (!phone) {
      setError('Phone number is required');
      setLoading(false);
      return;
    }

    // Clean phone number for validation
    const cleanPhone = phone.replace(/[^+\d]/g, '');
    
    if (cleanPhone.length < 8) {
      setError('Phone number must be at least 8 digits');
      setLoading(false);
      return;
    }

    if (!/^\+?[1-9]\d{7,14}$/.test(cleanPhone)) {
      setError('Please enter a valid phone number');
      setLoading(false);
      return;
    }

    // Format phone number with country code if not already present
    const formattedPhone = cleanPhone.startsWith('+') ? cleanPhone : `+${cleanPhone}`;

    try {
      // STEP 1: Check if user already exists with this phone number (opposite of forgot password)
      console.log('Step 1: Checking if user already exists with phone number:', formattedPhone);
      
      const checkUserParams = {
        requestParameters: {
          PhoneNumber: formattedPhone
        }
      };

      const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      };
      
      // Import the API helper and config
      const { MakeApiCallAsync, Config } = await import('@/lib/api-helper');
      
      const userCheckResponse = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_USER_BY_PHONE, null, checkUserParams, headers, "POST", true);
      
      console.log('User check response:', userCheckResponse);
      console.log('User check response data:', userCheckResponse.data);
      console.log('User check response data.data:', userCheckResponse.data?.data);
      console.log('User check response data.data type:', typeof userCheckResponse.data?.data);
      
      // Check if user already exists (opposite logic from forgot password)
      if (userCheckResponse.data && 
          !userCheckResponse.data.errorMessage && 
          !userCheckResponse.data.error && 
          userCheckResponse.data.data &&
          userCheckResponse.data.data !== "[]") {
        
        // Parse user data if it's a string
        let userData;
        if (typeof userCheckResponse.data.data === 'string') {
          try {
            userData = JSON.parse(userCheckResponse.data.data);
          } catch (e) {
            console.error('Error parsing user data:', e);
            userData = [];
          }
        } else {
          userData = userCheckResponse.data.data;
        }

        // If user data exists and is not empty, user already exists
        if (Array.isArray(userData) && userData.length > 0) {
          setError('An account with this phone number already exists. Please use a different phone number or try logging in.');
          setLoading(false);
          return;
        }
      }

      // STEP 2: If user doesn't exist, proceed with SMS verification for signup
      console.log('Step 2: User not found, proceeding with SMS verification for signup');
      
      // Send verification code using Twilio WhatsApp
      const response = await fetch('/api/sms/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          phoneNumber: formattedPhone,
          useWhatsApp: true  // Enable WhatsApp verification
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setConfirmationResult({ phoneNumber: formattedPhone });
        setUserDetails({...userDetails, mobileNo: formattedPhone});
        setFormStep('verification');
        setVerificationTimer(180); // 3 minutes = 180 seconds
        setVerificationExpired(false);
        setResendTimer(180); // Set resend timer to 3 minutes
      } else {
        throw new Error(data.error || 'Failed to send verification code');
      }
    } catch (err: any) {
      console.error('Error during phone verification:', err);
      console.error('Error code:', err.code);
      console.error('Error message:', err.message);
      
      let errorMessage = 'Failed to send verification code. Please try again.';
      
      if (err.code === 'auth/captcha-check-failed') {
        errorMessage = 'reCAPTCHA verification failed. Please refresh the page and try again.';
      } else if (err.code === 'auth/invalid-phone-number') {
        errorMessage = 'Invalid phone number format. Please check and try again.';
      } else if (err.code === 'auth/too-many-requests') {
        errorMessage = 'Too many attempts. Please wait a few minutes before trying again.';
      } else if (err.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your internet connection and try again.';
      } else if (err.message) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (resendTimer > 0) return;
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/sms/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phoneNumber: confirmationResult.phoneNumber }),
      });

      const data = await response.json();

      if (response.ok) {
        setVerificationTimer(180); // Reset 3-minute timer
        setVerificationExpired(false);
        setResendTimer(180); // Set resend timer to 3 minutes
      } else {
        throw new Error(data.error || 'Failed to resend verification code');
      }
    } catch (err: any) {
      setError('Failed to resend verification code');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (verificationExpired || verificationTimer === 0) {
      setError('Verification code has expired. Please request a new code.');
      return;
    }
    
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/sms/verify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          phoneNumber: confirmationResult.phoneNumber, 
          code: verificationCode 
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setVerificationTimer(0);
        setVerificationExpired(false);
        setFormStep('details');
      } else {
        throw new Error(data.error || 'Invalid verification code');
      }
    } catch (err: any) {
      setError(err.message || 'Invalid verification code');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDetailsSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validate form fields
    if (!userDetails.firstName.trim()) {
      setError('First name is required');
      setLoading(false);
      return;
    }

    if (!userDetails.lastName.trim()) {
      setError('Last name is required');
      setLoading(false);
      return;
    }

    if (!userDetails.email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userDetails.email)) {
      setError('Please enter a valid email address');
      setLoading(false);
      return;
    }

    if (!userDetails.password || userDetails.password.length < 8) {
      setError('Password must be at least 8 characters long');
      setLoading(false);
      return;
    }

    try {
      const requestParameters = {
        FirstName: userDetails.firstName,
        LastName: userDetails.lastName,
        EmailAddress: userDetails.email,
        Password: userDetails.password,
        MobileNo: userDetails.mobileNo,
        CityId: userDetails.cityId,
        StateProvinceId: userDetails.stateProvinceId,
        CountryID: userDetails.countryId,
        AddressLineOne: userDetails.cityId,
        PostalCode: userDetails.cityId
      };

      console.log('Request parameters:', requestParameters);

      // Import the API helper and config
      const { MakeApiCallAsync, Config } = await import('@/lib/api-helper');
      
      const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      };

      // Make the API call to create the user account
      const response = await MakeApiCallAsync(
        Config.END_POINT_NAMES.SIGNUP_USER, 
        null, 
        { requestParameters }, 
        headers, 
        "POST", 
        true
      );

      console.log('Create user response:', response);

      if (response.data && !response.data.errorMessage && !response.data.error) {
        // Account created successfully
        console.log('Account created successfully');
        
        // Import and show SweetAlert
        const Swal = (await import('sweetalert2')).default;
        
        await Swal.fire({
          title: 'Success!',
          text: 'Your account has been created successfully!',
          icon: 'success',
          confirmButtonText: 'Go to Login',
          confirmButtonColor: '#10b981',
          allowOutsideClick: false,
          allowEscapeKey: false
        });
        
        // Redirect to login page after user clicks OK
        window.location.href = '/login';
      } else {
        // Handle API error
        const errorMessage = response.data?.errorMessage || response.data?.error || 'Failed to create account';
        setError(errorMessage);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create account');
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold">
            {formStep === 'phone' && 'Get Started'}
            {formStep === 'verification' && 'Verify Your Phone'}
            {formStep === 'details' && 'Complete Your Profile'}
          </h2>
          <p className="mt-2 text-sm text-muted-foreground">
            {formStep === 'phone' && 'Enter your WhatsApp number to create an account'}
            {formStep === 'verification' && 'Enter the code we sent to your WhatsApp'}
            {formStep === 'details' && 'Just a few more details to complete your account'}
          </p>
        </div>



        <Card className="mt-8 p-8 shadow-xl bg-card/100">
          <div className="flex justify-center mb-8">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${formStep === 'phone' ? 'bg-primary text-primary-foreground' : 'bg-primary/20 text-primary'}`}>
                <Smartphone className="w-4 h-4" />
              </div>
              <div className={`w-16 h-1 ${formStep === 'phone' ? 'bg-primary/20' : formStep === 'verification' ? 'bg-primary' : 'bg-primary'}`} />
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${formStep === 'verification' ? 'bg-primary text-primary-foreground' : formStep === 'details' ? 'bg-primary' : 'bg-primary/20 text-primary'}`}>
                <CheckCircle2 className="w-4 h-4" />
              </div>
              <div className={`w-16 h-1 ${formStep === 'details' ? 'bg-primary' : 'bg-primary/20'}`} />
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${formStep === 'details' ? 'bg-primary text-primary-foreground' : 'bg-primary/20 text-primary'}`}>
                <User className="w-4 h-4" />
              </div>
            </div>
          </div>

          <motion.div
            key={formStep}
            variants={formVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ duration: 0.3 }}
          >
            {formStep === 'phone' && (
              <form onSubmit={handlePhoneSubmit} className="space-y-6">
                <div>
                  <Label className="block text-sm font-medium mb-2 text-center">Phone Number (WhatsApp)</Label>
                  <div className="flex flex-col items-center gap-2">
                    <div className="w-full max-w-[300px]">
                      <PhoneInput
                        country={userCountry}
                        value={phone}
                        onChange={(value) => {
                          setPhone(value);
                          setError('');
                        }}
                        enableSearch
                        searchPlaceholder="Search country..."
                        containerClass="w-full"
                        inputClass={`w-full p-4 border rounded-lg focus:ring-2 focus:ring-primary/50 ${error ? 'border-destructive' : ''}`}
                        buttonClass="!border-input !bg-background hover:!bg-accent"
                        dropdownClass="!bg-background !border-input"
                        disabled={loading}
                        countryCodeEditable={false}
                        isValid={(value, country) => {
                          if (!value) return false;
                          if (value.length < 8) return false;
                          if (!/^\+?[1-9]\d{1,14}$/.test(value)) return false;
                          return true;
                        }}
                      />
                    </div>
                    {error && (
                      <p className="text-sm text-destructive">{error}</p>
                    )}
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
                  disabled={loading}
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <>Continue <ArrowRight className="w-4 h-4" /></>
                  )}
                </Button>

                <div className="text-center text-sm mt-4">
                  <span className="text-muted-foreground">Already have an account? </span>
                  <Link href="/login" className="text-primary hover:text-primary/80 hover:underline transition-colors">
                    Login
                  </Link>
                </div>
              </form>
            )}

            {formStep === 'verification' && (
              <form onSubmit={handleVerificationSubmit} className="space-y-6">
                <div>
                  <Label className="block text-sm font-medium mb-4 text-center">Verification Code</Label>


                  {verificationExpired && (
                    <p className="text-sm text-red-600 mb-4 font-medium text-center">
                      Verification code has expired. Please request a new code.
                    </p>
                  )}
                  <div className="flex justify-center items-center">
                    <Input
                      type="text"
                      maxLength={6}
                      className="w-48 h-12 sm:w-56 sm:h-14 text-center text-lg sm:text-2xl font-semibold rounded-lg focus:ring-2 focus:ring-primary/50 transition-all"
                      value={verificationCode}
                      onChange={(e) => {
                        const value = e.target.value.replace(/\D/g, '').slice(0, 6);
                        setVerificationCode(value);
                      }}
                      onPaste={(e) => {
                        e.preventDefault();
                        const pastedData = e.clipboardData.getData('text').replace(/\D/g, '').slice(0, 6);
                        setVerificationCode(pastedData);
                      }}
                      placeholder="Enter 6-digit code"
                      style={{ fontSize: '14px' }}
                      disabled={loading}
                    />
                  </div>
                  <div className="mt-4 text-center">
                    <div className="flex justify-center items-center gap-4">
                      {verificationTimer > 0 && (
                        <div className="text-sm text-orange-600 font-medium">
                          Timer: {Math.floor(verificationTimer / 60)}:{(verificationTimer % 60).toString().padStart(2, '0')}
                        </div>
                      )}
                      <button
                         type="button"
                         onClick={handleResendCode}
                         className={`text-sm ${verificationTimer > 0 ? 'text-muted-foreground' : 'text-primary hover:underline'}`}
                         disabled={verificationTimer > 0 || loading}
                       >
                         Resend code
                       </button>
                    </div>
                  </div>
                </div>
                <Button
                  type="submit"
                  className="w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
                  disabled={loading || verificationCode.length !== 6}
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <>Verify <CheckCircle2 className="w-4 h-4" /></>
                  )}
                </Button>
              </form>
            )}

            {formStep === 'details' && (
              <form onSubmit={handleDetailsSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="block text-sm font-medium mb-2">First Name</Label>
                    <div className="relative">
                      <Input
                        type="text"
                        value={userDetails.firstName}
                        onChange={(e) => setUserDetails({...userDetails, firstName: e.target.value})}
                        className="pl-10"
                        required
                        disabled={loading}
                      />
                      <User className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                    </div>
                  </div>
                  <div>
                    <Label className="block text-sm font-medium mb-2">Last Name</Label>
                    <div className="relative">
                      <Input
                        type="text"
                        value={userDetails.lastName}
                        onChange={(e) => setUserDetails({...userDetails, lastName: e.target.value})}
                        className="pl-10"
                        required
                        disabled={loading}
                      />
                      <User className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                    </div>
                  </div>
                </div>
                <div>
                  <Label className="block text-sm font-medium mb-2">Email</Label>
                  <div className="relative">
                    <Input
                      type="email"
                      value={userDetails.email}
                      onChange={(e) => setUserDetails({...userDetails, email: e.target.value})}
                      className="pl-10"
                      required
                      disabled={loading}
                    />
                    <Mail className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                  </div>
                </div>
                <div>
                  <Label className="block text-sm font-medium mb-2">Password</Label>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      value={userDetails.password}
                      onChange={(e) => setUserDetails({...userDetails, password: e.target.value})}
                      className="pl-10 pr-10"
                      required
                      minLength={8}
                      disabled={loading}
                    />
                    <Lock className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={loading}
                    >
                      {showPassword ? (
                        <Eye className="w-4 h-4 text-muted-foreground" />
                      ) : (
                        <EyeOff className="w-4 h-4 text-muted-foreground" />
                      )}
                      <span className="sr-only">
                        {showPassword ? "Hide password" : "Show password"}
                      </span>
                    </Button>
                  </div>
                </div>
                <Button
                  type="submit"
                  className="w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
                  disabled={loading}
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    'Create Account'
                  )}
                </Button>
              </form>
            )}
          </motion.div>
        </Card>
      </div>
    </div>
  );
}