/*
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

/*
reset.css (part of editor.css)
================================

This file holds the "reset" requirements of CKEditor, as well as define the
default interface styles.

CKEditor includes two main "reset" class names in the DOM structure created for
editors:

    * .cke_reset: Intended to reset a specific element, but not its children.
      Because of this, only styles that will not be inherited can be defined.

    * .cke_reset_all: Intended to reset not only the element holding it, but
       also its child elements.

To understand why "reset" is needed, check the CKEditor Skin SDK:
http://docs.cksource.com/CKEditor_4.x/Skin_SDK/Reset
*/

/* Reset for single elements, not their children. */
.cke_reset {
    /* Do not include inheritable rules here. */
    margin: 0;
    padding: 0;
    border: 0;
    background: transparent;
    text-decoration: none;
    width: auto;
    height: auto;
    vertical-align: baseline;
    box-sizing: content-box;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    position: static;
    -webkit-transition: none;
    -moz-transition: none;
    -ms-transition: none;
    transition: none;
}

/* Reset for elements and their children. */
.cke_reset_all, .cke_reset_all * {
    /* The following must be identical to .cke_reset. */
    margin: 0;
    padding: 0;
    border: 0;
    background: transparent;
    text-decoration: none;
    width: auto;
    height: auto;
    vertical-align: baseline;
    box-sizing: content-box;
    -moz-box-sizing: content-box;
    -webkit-box-sizing: content-box;
    position: static;
    -webkit-transition: none;
    -moz-transition: none;
    -ms-transition: none;
    transition: none;

    /* These are rule inherited by all children elements. */
    border-collapse: collapse;
    font: normal normal normal 12px Arial,Helvetica,Tahoma,Verdana,Sans-Serif;
    color: $gray-darker;
    text-align: left;
    white-space: nowrap;
    cursor: auto;
    float: none;
}

.cke_reset_all {
    .cke_rtl * {
        text-align: right;
    }
    /* Defaults for some elements. */
    iframe {
        vertical-align: inherit;    /** For IE */
    }
    textarea {
        white-space: pre;
    }
    textarea, input[type="text"], input[type="password"] {
        cursor: text;
    }
    textarea[disabled], input[type="text"][disabled], input[type="password"][disabled] {
        cursor: default;
    }
    fieldset {
        padding: 10px;
        margin-top: 10px;
        border: 1px solid $hr-border;
        legend {
            padding: 0 5px;
        }
    }
    select {
        box-sizing: border-box;
        -moz-box-sizing: border-box;
        -webkit-box-sizing: border-box;
    }
}
