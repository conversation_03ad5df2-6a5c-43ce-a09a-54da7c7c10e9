<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Kunstmaan/BootstrapCK4-Skin @ GitHub</title>

    <link rel="stylesheet" href="css/bootstrapck-sample.css">
</head>

<body>
    <a href="https://github.com/Kunstmaan/BootstrapCK4-Skin"><img style="position: absolute; top: 0; right: 0; border: 0;" src="http://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png" alt="Fork me on GitHub" /></a>

    <div class="container">

        <div class="download">
            <a href="https://github.com/Kunstmaan/BootstrapCK4-Skin/zipball/master" onClick="javascript: _gaq.push(['_trackEvent', 'Download', 'BootstrapCKSkin-master.zip']);">
                <img border="0" width="90" src="https://github.com/images/modules/download/zip.png">
            </a>
            <a href="https://github.com/Kunstmaan/BootstrapCK4-Skin/tarball/master" onClick="javascript: _gaq.push(['_trackEvent', 'Download', 'BootstrapCKSkin-master.tar.gz']);">
                <img border="0" width="90" src="https://github.com/images/modules/download/tar.png">
            </a>
        </div>

        <h1>
            <a href="https://github.com/Kunstmaan/BootstrapCK4-Skin" onClick="javascript: _gaq.push(['_trackEvent', 'Outbound Links', 'github.com/Kunstmaan/BootstrapCK4-Skin']);">BootstrapCK4-Skin</a>
            <span>by <a href="https://github.com/Kunstmaan"onClick="javascript: _gaq.push(['_trackEvent', 'Outbound Links', 'github.com/Kunstmaan']);">Kunstmaan</a></span>
        </h1>

        <!-- Demo -->
        <h2>Demo</h2>
        <form action="sample_posteddata.php" method="post">
            <p>
                <textarea class="ckeditor" id="editor1" name="editor1" cols="100" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
            </p>
        </form>

        <p class="twitter">Like what you see? Let the world know!
            <a href="http://twitter.com/share" class="twitter-share-button" data-url="http://kunstmaan.github.com/BootstrapCK4-Skin/" data-via="" data-count="horizontal" data-text="A CKEditor skin based on @twbootstrap by Kunstmaan">Tweet</a>
        </p>

        <!-- About -->
        <h2>About</h2>
        <p>The BootstrapCK4-Skin is a skin for <a href="http://ckeditor.com/" target="_blank" onClick="javascript: _gaq.push(['_trackEvent', 'Outbound Links', 'ckeditor.com']);">CKEditor4</a> based on <a href="http://getbootstrap.com/" target="_blank" onClick="javascript: _gaq.push(['_trackEvent', 'Outbound Links', 'twitter.github.com/bootstrap/']);">Twitter Bootstrap3</a> styles.</p>
        <p><a href="http://sass-lang.com/" target="_blank">Sass</a> is used to rewrite the editor's styles and <a href="http://gruntjs.com/" target="_blank">Grunt</a> to be able to watch, convert and minify the sass into css files. These files aren't really needed for the simple use of the skin, but handy if you want to make some adjustments to it.</p>
        <p>For more information about skins, please check the <a href="http://docs.cksource.com/CKEditor_4.x/Skin_SDK" target="_blank">CKEditor Skin SDK</a></p>

        <!-- Installation -->
        <h2>Installation</h2>
        <h3>Just skin please</h3>

        <p>Add the whole bootstrapck folder to the skin folder.<br />
        In ckeditor.js and config.js change the skin name to "bootstrapck".<br />
        Done!</p>

        <h3>The whole skin - sass - grunt package</h3>

        <p>All the sass files are included in the bootstrapck folder, so first follow the 'just skin please'-steps<br />
        Now add the Gruntfile.js and the package.json to de ckeditor folder.</p>
        <pre>npm install <br />grunt build</pre>
        <p>You can start tampering now.</p>
        <p>Or if you'd like to adjust the icons, use the bootstrapck-dev folder instead.</p>

        <!-- Authors / Contact -->
        <h2>Authors</h2>
        <p>Indri Kenens (<EMAIL>)</p>

        <h2>Contact</h2>
        <p>Kunstmaan (<EMAIL>)</p>

        <!-- Download -->
        <h2>Download</h2>
        <p>
          You can download this project in either
          <a href="https://github.com/Kunstmaan/BootstrapCK4-Skin/zipball/master" onClick="javascript: _gaq.push(['_trackEvent', 'Download', 'BootstrapCKSkin-master.zip']);">zip</a> or
          <a href="https://github.com/Kunstmaan/BootstrapCK4-Skin/tarball/master" onClick="javascript: _gaq.push(['_trackEvent', 'Download', 'BootstrapCKSkin-master.tar.gz']);">tar formats.</a>
        </p>
        <p>You can also clone the project with <a href="http://git-scm.com" onClick="javascript: _gaq.push(['_trackEvent', 'Outbound Links', 'git-scm.com']);">Git</a>
          by running: <pre>$ git clone git://github.com/Kunstmaan/BootstrapCK4-Skin</pre></p>

        <!-- Previous version -->
        <h2>Previous version</h2>
        <p>If you would like to get the Bootstrap2 skin for CKeditor3, <a href="https://github.com/Kunstmaan/BootstrapCK-Skin" target="_blank">here</a>'s the previous version.</p>

        <!-- Footer -->
        <div class="footer">
          get the source code on GitHub : <a href="https://github.com/Kunstmaan/BootstrapCK4-Skin" onClick="javascript: _gaq.push(['_trackEvent', 'Outbound Links', 'github.com/Kunstmaan/BootstrapCK4-Skin']);">Kunstmaan/BootstrapCK4-Skin</a>
        </div>
    </div>


<!-- ckeditor -->
<script src="../../../ckeditor.js"></script>

<!-- jQuery -->
<script src="js/jquery-1.11.0.min.js"></script>

<!-- Google Analytics -->
<script src="js/analytics.js"></script>
<script type="text/javascript">
  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', 'UA-********-1']);
  _gaq.push(['_trackPageview']);

  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();
</script>

<!-- Twitter -->
<script type="text/javascript">
  (function(){
      var twitterWidgets = document.createElement('script');
      twitterWidgets.type = 'text/javascript';
      twitterWidgets.async = true;
      twitterWidgets.src = 'http://platform.twitter.com/widgets.js';
      twitterWidgets.onload = _ga.trackTwitter;
      document.getElementsByTagName('head')[0].appendChild(twitterWidgets);
  })();
</script>


</body>
</html>
