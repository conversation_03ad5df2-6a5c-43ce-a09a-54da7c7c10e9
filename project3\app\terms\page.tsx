'use client';

import { <PERSON>readcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';

export default function TermsPage() {
  const { t, primaryColor } = useSettings();

  const sections = [
    {
      title: 'Purchase Guidelines',
      content: 'Before buying any product, please to check the content carefully to be sure its exactly what have been requested weather it is courses, ebooks, printed books or medical application account). We disclaim our responsibility from any mistake made by the customer regarding wrong request. In the same time we will do our best to try to be in the side of our highly valuable customer.'
    },
    {
      title: 'International Customers',
      content: 'Concerning those who live outside Iraq, we provide link guarantee for limited time and you have to download the course through 1 month from the time you receive it, otherwise we are not responsible for link expire or deleting.'
    },
    {
      title: 'Refund Policy',
      content: 'The only situation in which refound it is possible, is that when the mistake made by our side like in providing different content from that have been requested.'
    },
    {
      title: 'Account Usage',
      content: 'Any time we detect using of the medical account on more than the allowed number of devices, the account will be blocked and the refund is not applicable.'
    },
    {
      title: 'Intellectual Property',
      content: 'Our material is highly valuable and it take a long time and hard work to provide them, so any sharing of these material will give us the right not to deal in the future with the customer who use it not for personal purpose.'
    }
  ];

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Terms & Conditions</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Terms & Conditions</h1>

        <div className="space-y-6">
          {sections.map((section, index) => (
            <Card key={index} className="p-6">
              <h2 className="text-xl font-semibold mb-4" style={{ color: primaryColor }}>
                {section.title}
              </h2>
              <p className="text-muted-foreground">{section.content}</p>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center text-sm text-muted-foreground">
          <p>Last updated: {new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </div>
  );
}