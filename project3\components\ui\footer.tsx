'use client';

import { Facebook, Mail } from 'lucide-react';
import WhatsAppIcon from '@/components/icons/WhatsAppIcon';
import MessengerIcon from '@/components/icons/MessengerIcon';
import TelegramIcon from '@/components/icons/TelegramIcon';
import { useState } from 'react';
import { useGoogleReCaptcha } from 'react-google-recaptcha-v3';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';

export function Footer() {
  const { primaryColor, primaryTextColor, t } = useSettings();
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const { executeRecaptcha } = useGoogleReCaptcha();

  const handleSubscribe = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate email
    if (!email || !email.trim()) {
      setMessage({ type: 'error', text: 'Please enter a valid email address.' });
      return;
    }

    if (!executeRecaptcha) {
      setMessage({ type: 'error', text: 'reCAPTCHA not available. Please refresh the page and try again.' });
      console.error('Execute recaptcha not yet available');
      return;
    }

    try {
      setIsLoading(true);
      setMessage({ type: '', text: '' });

      // Execute reCAPTCHA
      let token;
      try {
        token = await executeRecaptcha('subscribe');
        if (!token) {
          throw new Error('reCAPTCHA verification failed');
        }
      } catch (recaptchaError) {
        console.error('reCAPTCHA error:', recaptchaError);
        setMessage({ type: 'error', text: 'reCAPTCHA verification failed. Please try again.' });
        return;
      }
      
      // Make API request
      const apiUrl = `${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}api/v1/dynamic/dataoperation/insert-subscriber`;
      console.log('Making request to:', apiUrl);
      
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requestParameters: {
            SubscriberEmail: email.trim()
          }
        })
      });

      console.log('Response status:', response.status);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Response data:', data);

      if (data.statusCode === 200 || response.ok) {
        setMessage({ type: 'success', text: 'Successfully subscribed to our newsletter!' });
        setEmail('');
      } else {
        setMessage({ type: 'error', text: data.message || data.errorMessage || 'Failed to subscribe. Please try again.' });
      }
    } catch (error) {
      console.error('Subscription error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setMessage({ type: 'error', text: 'Network error. Please check your connection and try again.' });
      } else {
        setMessage({ type: 'error', text: `Subscription failed: ${error instanceof Error ? error.message : 'Unknown error'}` });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <footer className="w-full">
      <div className="py-8 sm:py-12 md:py-16" style={{ backgroundColor: primaryColor, color: primaryTextColor }}>
        <div className="container mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 footer-grid gap-6 sm:gap-8 md:gap-10">
          {/* About Section */}
          <div className="space-y-6 sm:col-span-2 lg:col-span-1">
            <div className="flex items-center gap-2">
              <img
                src={`${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}content/commonImages/otherImages/18b_logo2x.png`.replace(/\//g, '/')}
                alt="Logo"
                className="h-12 sm:h-16 lg:h-14 xl:h-20 w-auto bg-white p-2 rounded-md"
              />
            </div>
            <p className="text-sm sm:text-base lg:text-sm xl:text-lg footer-text opacity-90 leading-relaxed">
              We are professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time.
            </p>
            <div className="flex gap-4 lg:gap-3 xl:gap-6 social-icons">
              <Link href="https://www.facebook.com/codemedicalapps/" target="_blank" rel="noopener noreferrer" className="hover:opacity-70 transition-opacity">
                <Facebook className="h-5 w-5" />
              </Link>
              <Link href="https://t.me/codemedicalapps" target="_blank" rel="noopener noreferrer" className="hover:opacity-70 transition-opacity">
                <TelegramIcon className="h-5 w-5" />
              </Link>
              <Link href="https://wa.me/*************" target="_blank" rel="noopener noreferrer" className="hover:opacity-70 transition-opacity">
                <WhatsAppIcon className="h-5 w-5" />
              </Link>
              <Link href="https://m.me/***************" target="_blank" rel="noopener noreferrer" className="hover:opacity-70 transition-opacity">
                <MessengerIcon className="h-5 w-5" style={{ color: '#00B2FF' }} />
              </Link>
            </div>
          </div>

          {/* Quick Links and Customer Area - Better tablet layout */}
          <div className="grid grid-cols-2 gap-6 sm:gap-8 lg:gap-4 xl:gap-10 footer-links col-span-1 sm:col-span-2 lg:col-span-2">
            <div className="space-y-4 lg:space-y-3 xl:space-y-6">
              <h3 className="text-base sm:text-lg lg:text-base xl:text-xl footer-heading font-semibold">{t('quickLinks')}</h3>
              <ul className="space-y-2 sm:space-y-3 lg:space-y-2 xl:space-y-4 text-sm sm:text-base lg:text-sm xl:text-lg footer-text">
                <li><Link href="/about" className="hover:opacity-70 transition-opacity">{t('about')}</Link></li>
                <li><Link href="/contact" className="hover:opacity-70 transition-opacity">{t('contact')}</Link></li>
                <li><Link href="/hot-deals" className="hover:opacity-70 transition-opacity">{t('hotDeals')}</Link></li>
                <li><Link href="/terms" className="hover:opacity-70 transition-opacity">Terms & Conditions</Link></li>
                <li><Link href="/login" className="hover:opacity-70 transition-opacity">{t('login')}</Link></li>
                <li><Link href="/signup" className="hover:opacity-70 transition-opacity">{t('signup')}</Link></li>
              </ul>
            </div>

            <div className="space-y-4 md:space-y-6">
              <h3 className="text-base sm:text-lg md:text-xl font-semibold">{t('customerArea')}</h3>
              <ul className="space-y-2 sm:space-y-3 md:space-y-4 text-sm sm:text-base md:text-lg">
                <li><Link href="/account" className="hover:opacity-70 transition-opacity">{t('myAccount')}</Link></li>
                <li><Link href="/orders" className="hover:opacity-70 transition-opacity">{t('orders')}</Link></li>
                <li><Link href="/cart" className="hover:opacity-70 transition-opacity">{t('cart')}</Link></li>
                <li><Link href="/wishlist" className="hover:opacity-70 transition-opacity">{t('wishlist')}</Link></li>
                <li><Link href="/payment-methods" className="hover:opacity-70 transition-opacity">{t('paymentMethods')}</Link></li>
              </ul>
            </div>
          </div>

          {/* Contact Us - Improved tablet spacing */}
          <div className="space-y-4 md:space-y-6 sm:col-span-2 lg:col-span-1">
            <h3 className="text-base sm:text-lg md:text-xl font-semibold">{t('contact')}</h3>
            <ul className="space-y-2 sm:space-y-3 md:space-y-4 text-sm sm:text-base md:text-lg">
              <li className="flex items-center gap-2">
                <span className="opacity-75">{t('location')}:</span>
                <span>Iraq</span>
              </li>
              <li className="flex items-center gap-2">
                <span className="opacity-75">{t('callUs')}:</span>
                <a href="tel:+*************" className="hover:opacity-70 transition-opacity">+964 ************</a>
              </li>
              <li className="flex items-center gap-2">
                <span className="opacity-75">{t('emailUs')}:</span>
                <a href="mailto:<EMAIL>" className="hover:opacity-70 transition-opacity"><EMAIL></a>
              </li>
            </ul>

            {/* Newsletter Subscription - Improved for tablets */}
            <div className="mt-6 md:mt-8">
              <h3 className="text-base sm:text-lg md:text-xl font-semibold mb-4">{t('newsletter')}</h3>
              <form onSubmit={handleSubscribe} className="space-y-4">
                <div className="flex flex-col gap-2">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder={t('enterEmail')}
                    required
                    className="w-full px-4 py-3 lg:py-2 xl:py-4 newsletter-input text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50 text-sm lg:text-sm xl:text-base"
                  />
                </div>
                {message.text && (
                  <p className={`text-sm text-center ${message.type === 'success' ? 'text-green-400' : 'text-red-400'}`}>
                    {message.text}
                  </p>
                )}
                <p className="text-xs opacity-75 text-center">{t('newsletterDisclaimer')}</p>
                </form>
          </div>
          </div>
        </div>

        {/* Bottom Bar - Improved tablet spacing */}
        <div className="mt-6 md:mt-8 pt-6 md:pt-8 border-t text-center opacity-75 text-sm sm:text-base md:text-lg px-4" style={{ borderColor: primaryTextColor }}>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4">
            <p>© 2024 Code Medical. All rights reserved.</p>
            <span className="hidden sm:inline">•</span>
            <p>
              Powered by{' '}
              <a 
                href="https://perfectjobline.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="hover:opacity-70 transition-opacity underline"
              >
                perfectjobline
              </a>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}