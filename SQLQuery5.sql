﻿SELECT TOP (1000) [OrderID]
      ,[CustomerID]
      ,[OrderNumber]
      ,[OrderDateUTC]
      ,[LatestStatusID]
      ,[ShippingAddressID]
      ,[OrderSubtotalInclTax]
      ,[OrderSubtotalExclTax]
      ,[OrderTotalDiscountAmount]
      ,[OrderTotalShippingCharges]
      ,[OrderTotalAttributeCharges]
      ,[OrderTax]
      ,[OrderTotal]
      ,[Point]
      ,[ExchangeRate]
      ,[DiscountId]
      ,[TotalFinal]
      ,[CurrencyCode]
  FROM [codemedi_shop].[dbo].[Orders]
DELETE FROM OrderProductAttributeMapping
WHERE OrderItemID IN (
    SELECT oi.OrderItemID
    FROM OrderItems oi
    INNER JOIN Orders o ON oi.OrderID = o.OrderID
    WHERE o.CustomerID = 64
);

-- 2) احذف تفاصيل الشحن المرتبطة بالـ OrderItems تبع Customer 64
DELETE FROM OrderShippingDetail
WHERE OrderItemID IN (
    SELECT oi.OrderItemID
    FROM OrderItems oi
    INNER JOIN Orders o ON oi.OrderID = o.OrderID
    WHERE o.CustomerID = 64
);

-- 3) احذف الـ OrderNotes المرتبطة بالأوردرات تبع Customer 64
DELETE FROM OrderNotes
WHERE OrderID IN (
    SELECT o.OrderID
    FROM Orders o
    WHERE o.CustomerID = 64
);

-- 4) احذف الـ OrderItems نفسها
DELETE FROM OrderItems
WHERE OrderID IN (
    SELECT OrderID FROM Orders WHERE CustomerID = 64
);
DELETE FROM OrdersPayments
WHERE OrderID IN (
    SELECT o.OrderID
    FROM Orders o
    WHERE o.CustomerID = 64
);
DELETE FROM OrderStatusesMapping
WHERE OrderID IN (
    SELECT o.OrderID
    FROM Orders o
    WHERE o.CustomerID = 64
);
DELETE FROM PointTransactions
WHERE OrderID IN (
    SELECT o.OrderID
    FROM Orders o
    WHERE o.CustomerID = 64
);

DELETE FROM Orders
WHERE CustomerID = 64;
