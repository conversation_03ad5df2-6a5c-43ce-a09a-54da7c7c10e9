'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useUser } from '@/contexts/user-context';
import { Package, ArrowLeft, Calendar, CreditCard, MapPin, Phone, Mail, User, LogIn, Star } from 'lucide-react';
import ProductReview from '@/components/ui/product-review';
import { decryptOrderId, decryptValue } from '@/lib/encryption';

export default function OrderDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { t } = useSettings();
  const { user, isLoggedIn, isLoading, token } = useUser();
  const [orderDetails, setOrderDetails] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [showReviewFor, setShowReviewFor] = useState<number | null>(null);

  // Get encrypted order ID from URL params
  const encryptedOrderId = params.orderId as string;

  // Decrypt the order ID and query parameters
  let orderId: string;
  let quickOrderTotal: string | null = null;
  let quickOrderDate: string | null = null;

  try {
    orderId = decryptOrderId(encryptedOrderId);

    // Decrypt query parameters if available
    const encryptedTotal = searchParams.get('t');
    const encryptedDate = searchParams.get('d');

    if (encryptedTotal) {
      try {
        quickOrderTotal = decryptValue(encryptedTotal);
        console.log('🔐 Decrypted order total:', quickOrderTotal);
      } catch (error) {
        console.warn('⚠️ Failed to decrypt order total:', error);
      }
    }

    if (encryptedDate) {
      try {
        quickOrderDate = decryptValue(encryptedDate);
        console.log('🔐 Decrypted order date:', quickOrderDate);
      } catch (error) {
        console.warn('⚠️ Failed to decrypt order date:', error);
      }
    }

    // Log decryption for debugging
    console.log('🔐 Order ID Decryption:', {
      encrypted: encryptedOrderId,
      decrypted: orderId,
      isValidNumber: !isNaN(parseInt(orderId)),
      hasQuickTotal: !!quickOrderTotal,
      hasQuickDate: !!quickOrderDate
    });

    // If decryption returns the same value as input, it might have failed
    if (orderId === encryptedOrderId && (encryptedOrderId.includes('-') || encryptedOrderId.includes('_'))) {
      console.warn('⚠️ Decryption may have failed, encrypted and decrypted values are the same');
    }
  } catch (error) {
    console.error('❌ Order ID decryption failed:', error);
    // Fallback: try to use the encrypted ID as-is if it's numeric
    orderId = encryptedOrderId;
  }

  // Fetch order details function
  const fetchOrderDetails = async () => {
    console.log('fetchOrderDetails called - Starting API request for order:', orderId);

    // Validate decrypted order ID
    const numericOrderId = parseInt(orderId);
    if (isNaN(numericOrderId) || numericOrderId <= 0) {
      console.error('❌ Invalid order ID after decryption:', orderId);
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      console.log('Making API call to /api/orders/details with OrderId:', numericOrderId);

      // Get JWT token from user context
      const authHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      };

      // Add JWT token to headers if available
      if (token) {
        authHeaders['Authorization'] = `Bearer ${token}`;
        console.log('🔐 Added JWT token to order details request');
      }

      const response = await fetch('/api/orders/details', {
        method: 'POST',
        headers: authHeaders,
        body: JSON.stringify({
          requestParameters: {
            OrderId: numericOrderId,
            // UserId removed - will be auto-injected from JWT token
            recordValueJson: "[]"
          }
        })
      });

      const data = await response.json();
      console.log('Order details response:', data);

      if (data && data.data) {
        let detailsData;

        // Check if data is a string that needs parsing or already parsed
        if (typeof data.data === 'string') {
          detailsData = JSON.parse(data.data);
        } else {
          detailsData = data.data;
        }

        console.log('Parsed order details:', detailsData);

        if (Array.isArray(detailsData) && detailsData.length > 0) {
          console.log('🔍 Order Details: Available fields in first order:', Object.keys(detailsData[0]));
          console.log('🔍 Order Details: Status-related fields:', {
            StatusID: detailsData[0].StatusID,
            OrderStatusId: detailsData[0].OrderStatusId,
            StateId: detailsData[0].StateId,
            LatestStatusID: detailsData[0].LatestStatusID,
            LatestStatusId: detailsData[0].LatestStatusId,
            LatestStatusName: detailsData[0].LatestStatusName,
            StatusName: detailsData[0].StatusName
          });
          setOrderDetails(detailsData);
        } else {
          console.log('No order details found or invalid data structure');
          setOrderDetails([]);
        }
      } else {
        console.error('Invalid response structure:', data);
        setOrderDetails([]);
      }
    } catch (error) {
      console.error('Error fetching order details:', error);
      setOrderDetails([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch order details when component mounts and dependencies change
  useEffect(() => {
    const userId = user?.UserID || user?.UserId;
    console.log('Order details page - User context:', { isLoggedIn, user, userID: user?.UserID, userId: user?.UserId, finalUserId: userId, orderId });
    if (isLoggedIn && userId && orderId) {
      console.log('Order details page - Fetching order details for UserID:', userId, 'OrderID:', orderId);
      fetchOrderDetails();
    } else {
      console.log('Order details page - Not fetching details. isLoggedIn:', isLoggedIn, 'userId:', userId, 'orderId:', orderId);
    }
  }, [isLoggedIn, user, orderId]);

  // Early returns for loading and authentication states
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-6">
            <Card>
              <div className="p-6 space-y-4">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-32" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-20" />
                  <Skeleton className="h-20" />
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!isLoggedIn) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card className="max-w-md mx-auto">
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <LogIn className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-lg font-medium mb-2">Login Required</h3>
            <p className="text-muted-foreground mb-4">Please log in to view order details</p>
            <Button asChild className="w-full">
              <Link href={`/login?redirect=/orders/${params.orderId}`}>
                Login to Continue
              </Link>
            </Button>
          </div>
        </Card>
      </div>
    );
  }







  const orderInfo = orderDetails[0] || {};
  
  // Status mapping based on StatusID
  const getStatusInfo = (statusId: number | string) => {
    const id = parseInt(statusId?.toString() || '1');
    switch(id) {
      case 1:
        return { name: 'Active', color: 'bg-blue-100 text-blue-800' };
      case 2:
        return { name: 'In Progress', color: 'bg-orange-100 text-orange-800' };
      case 3:
        return { name: 'Completed', color: 'bg-green-100 text-green-800' };
      case 4:
        return { name: 'Returned', color: 'bg-purple-100 text-purple-800' };
      case 5:
        return { name: 'Refunded', color: 'bg-indigo-100 text-indigo-800' };
      case 6:
        return { name: 'Cancelled', color: 'bg-red-100 text-red-800' };
      default:
        return { name: 'Unknown', color: 'bg-gray-100 text-gray-800' };
    }
  };

  // Check if order is completed (state ID = 3) to allow reviews
  // Try multiple possible status field names from the API response
  const statusId = orderInfo.StatusID || orderInfo.OrderStatusId || orderInfo.StateId || orderInfo.LatestStatusID || orderInfo.LatestStatusId || 1;
  const canWriteReview = parseInt(statusId.toString()) === 3;

  // Also check for status name from API response
  const statusName = orderInfo.LatestStatusName || orderInfo.StatusName || null;

  console.log("🔍 Order Details: Status debugging", {
    orderInfo: orderInfo,
    statusId: statusId,
    statusName: statusName,
    availableFields: Object.keys(orderInfo)
  });

  const handleReviewSubmitted = () => {
    setShowReviewFor(null);
    // Optionally refresh order details or show success message
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/orders">Orders</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Order #{orderId}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <Button variant="outline" className="mb-6" asChild>
        <Link href="/orders">
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Orders
        </Link>
      </Button>

      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Order Details</h1>

        {loading ? (
          <div className="space-y-6">
            <Card>
              <div className="p-6 space-y-4">
                <Skeleton className="h-8 w-64" />
                <Skeleton className="h-4 w-32" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Skeleton className="h-20" />
                  <Skeleton className="h-20" />
                </div>
              </div>
            </Card>
            <Card>
              <div className="p-6 space-y-4">
                <Skeleton className="h-6 w-32" />
                {Array.from({ length: 3 }).map((_, i) => (
                  <Skeleton key={i} className="h-16" />
                ))}
              </div>
            </Card>
          </div>
        ) : orderDetails.length > 0 ? (
          <div className="space-y-6">
            {/* Order Summary */}
            <Card>
              <div className="p-6">
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                  <div>
                    <h2 className="text-2xl font-bold">
                      Order #{orderInfo.OrderNumber || orderInfo.OrderID || orderId}
                    </h2>
                    <p className="text-muted-foreground flex items-center gap-2 mt-1">
                      <Calendar className="h-4 w-4" />
                      Placed on {loading ? (quickOrderDate || 'Loading...') : (orderInfo.OrderDate || orderInfo.CreatedOn || quickOrderDate || 'N/A')}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold">
                      ${loading ? 
                        (quickOrderTotal ? `${quickOrderTotal}` : 'Loading...') : 
                        (orderInfo.TotalFinal || orderInfo.TotalAmount || quickOrderTotal || '0.00')
                      }
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {loading ? 'Loading items...' : `${orderDetails.length} item(s)`}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Customer Information */}
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Customer Information
                    </h3>
                    <div className="space-y-2 text-sm">
                      <p><strong>Name:</strong> {user?.FirstName} {user?.LastName}</p>
                      <p className="flex items-center gap-2">
                        <Mail className="h-3 w-3" />
                        {user?.Email || user?.EmailAddress}
                      </p>
                      <p className="flex items-center gap-2">
                        <Phone className="h-3 w-3" />
                        {user?.PhoneNumber || user?.PhoneNo || 'N/A'}
                      </p>
                    </div>
                  </div>

                  {/* Payment Information */}
                  <div>
                    <h3 className="font-semibold mb-3 flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Payment Information
                    </h3>
                    <div className="space-y-2 text-sm">
                      <p><strong>Payment Method:</strong> {orderInfo.PaymentMethodName || (orderInfo.PaymentMethod === 6 ? 'Cash on Delivery' : 'Online Payment')}</p>
                      <p><strong>Currency:</strong> USD</p>
                      <p><strong>Status:</strong>
                        {(() => {
                          // Use status name from API if available, otherwise map from status ID
                          if (statusName) {
                            // Map API status names to display colors
                            let statusColor = 'bg-gray-100 text-gray-800'; // default
                            switch (statusName.toLowerCase()) {
                              case 'active':
                                statusColor = 'bg-blue-100 text-blue-800';
                                break;
                              case 'in progress':
                              case 'inprogress':
                                statusColor = 'bg-orange-100 text-orange-800';
                                break;
                              case 'completed':
                                statusColor = 'bg-green-100 text-green-800';
                                break;
                              case 'returned':
                                statusColor = 'bg-purple-100 text-purple-800';
                                break;
                              case 'refunded':
                                statusColor = 'bg-indigo-100 text-indigo-800';
                                break;
                              case 'cancelled':
                                statusColor = 'bg-red-100 text-red-800';
                                break;
                            }
                            return (
                              <span className={`ml-1 px-2 py-1 rounded-full text-xs ${statusColor}`}>
                                {statusName}
                              </span>
                            );
                          } else {
                            // Fallback to status ID mapping
                            const statusInfo = getStatusInfo(statusId);
                            return (
                              <span className={`ml-1 px-2 py-1 rounded-full text-xs ${statusInfo.color}`}>
                                {statusInfo.name}
                              </span>
                            );
                          }
                        })()}
                      </p>
                      {canWriteReview && (
                        <p className="text-sm text-green-600 mt-1 flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          You can now write reviews for your products
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Order Items */}
            <Card>
              <div className="p-6">
                <h3 className="font-semibold mb-4 flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Order Items
                </h3>
                <div className="space-y-4">
                  {orderDetails.map((item, index) => {
                    const productImage = item.ProductImageUrl || item.ImageUrl || item.ProductImage;
                    const adminBaseUrl = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com/';
                    
                    return (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div className="w-16 h-16 bg-muted rounded-lg overflow-hidden flex items-center justify-center">
                            {productImage ? (
                              <img
                                src={productImage.startsWith('http') ? productImage : `${adminBaseUrl}${productImage}`}
                                alt={item.ProductName || item.ItemName || 'Product'}
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const parent = target.parentElement;
                                  if (parent) {
                                    parent.innerHTML = '<svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>';
                                  }
                                }}
                              />
                            ) : (
                              <Package className="h-8 w-8 text-gray-400" />
                            )}
                          </div>
                          <div>
                            <h4 className="font-medium">
                              {item.ProductName || item.ItemName || 'Medical Product'}
                            </h4>
                            <p className="text-sm text-muted-foreground">
                              Quantity: {item.Quantity || item.OrderQuantity || 1}
                            </p>
                            {item.ProductDescription && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {item.ProductDescription}
                              </p>
                            )}
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            ${(item.UnitPrice || item.Price || item.ItemPrice || 0).toFixed(2)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Total: ${((item.UnitPrice || item.Price || 0) * (item.Quantity || 1)).toFixed(2)}
                          </p>
                          {canWriteReview && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="mt-2"
                              onClick={() => setShowReviewFor(showReviewFor === item.ProductID ? null : item.ProductID)}
                            >
                              <Star className="h-3 w-3 mr-1" />
                              {showReviewFor === item.ProductID ? 'Cancel' : 'Write Review'}
                            </Button>
                          )}
                        </div>
                        </div>
                        
                        {/* Review Component */}
                        {canWriteReview && showReviewFor === item.ProductID && (
                          <div className="mt-4">
                            <ProductReview
                              productId={item.ProductID}
                              productName={item.ProductName || item.ItemName || 'Medical Product'}
                              onReviewSubmitted={handleReviewSubmitted}
                            />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>

                {/* Order Total Summary */}
                <div className="mt-6 pt-4 border-t">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold">Total Amount:</span>
                    <span className="text-2xl font-bold">
                      ${loading ? 
                        (quickOrderTotal ? `${quickOrderTotal}` : 'Loading...') : 
                        (orderInfo.TotalFinal || orderInfo.TotalAmount || quickOrderTotal || '0.00')
                      }
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        ) : (
          <Card>
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <Package className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">Order Not Found</h3>
              <p className="text-muted-foreground mb-4">
                The order you're looking for doesn't exist or you don't have permission to view it.
              </p>
              <Button asChild>
                <Link href="/orders">
                  Back to Orders
                </Link>
              </Button>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}