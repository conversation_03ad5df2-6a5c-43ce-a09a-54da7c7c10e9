# Changelog

## v1.2.2 (4.0.x, 4.1.x, 4.2.x, 4.3.x, 4.4.x)

  * [FIX] : Fixes ckeditor style overriden by pbckcode plugin

## v1.2.1 (4.0.x, 4.1.x, 4.2.x, 4.3.x)

  * [FIX] : problem with minification for CKEditor Builder
  * [FIX] : multiples script loads when you instanciate several CKEditor on the same page

From the v1.2.1, ACE Editor is now loaded from the [jsDelivr CDN](http://www.jsdelivr.com/). The loading will be faster and the plugin size will be smaller.

## v1.2.0 (4.0.x, 4.1.x, 4.2.x, 4.3.x)

  * [FIX] : fix problem of external files loading
  * [IMPROVEMENT] : load external files only once
  * [FEATURE] : tab size settings
  * [FIX] : added CKEditor ACF to parse already saved code

## v1.1.0 (4.0.x, 4.1.x)

  * [FEATURE] : added several syntax highlighters (Prism.js, Highlight.js, SyntaxHighlighter.js)
