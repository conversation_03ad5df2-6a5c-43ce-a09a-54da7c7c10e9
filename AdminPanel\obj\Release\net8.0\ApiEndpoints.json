[{"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiBlogController", "Method": "GetBlogById", "RelativePath": "api/v1/blog/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiBlogController", "Method": "GetBlogsList", "RelativePath": "api/v1/blog/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNo", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "DownloadDigitalFile", "RelativePath": "api/v1/common/download-digital-file/{order_item_id}/{user_id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "order_item_id", "Type": "System.Int32", "IsRequired": true}, {"Name": "user_id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "EncryptPassword", "RelativePath": "api/v1/common/en-ur-drow-pass-rndom/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "GetCouponCodeDiscountedValue", "RelativePath": "api/v1/common/get-coupon-code-discount-value/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "GetCustomerLatestCartItems", "RelativePath": "api/v1/common/get-customer-cart-items/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "GetStripePublishableKey", "RelativePath": "api/v1/common/get-strp-pub-key/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "GetLocalizationControlsJsonDataCstmPortal", "RelativePath": "api/v1/common/localization-cstm-portal/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "PostCustomerOrderDirect", "RelativePath": "api/v1/common/post-order-direct", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Entities.DBInheritedModels.PostCustomerOrderRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "PostCustomerOrder", "RelativePath": "api/v1/common/post-order/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "ResetPasswordByPhone", "RelativePath": "api/v1/common/reset-password-by-phone/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "ResetPasswordFirebase", "RelativePath": "api/v1/common/reset-password-firebase/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "TestNotifications", "RelativePath": "api/v1/common/test-notifications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "ValidateEmailAndSendOTP", "RelativePath": "api/v1/common/validate-email-send-otp/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiCommonController", "Method": "ValidateOTPAndChangePassword", "RelativePath": "api/v1/common/validate-otp-change-password/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiDynamicController", "Method": "DataOperation", "RelativePath": "api/v1/dynamic/dataoperation/{UrlName}", "HttpMethod": "", "IsController": true, "Order": 0, "Parameters": [{"Name": "UrlName", "Type": "System.String", "IsRequired": false}, {"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiProductsController", "Method": "GetAllProducts", "RelativePath": "api/v1/products/get-all-products", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "param", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiVerificationController", "Method": "CleanupExpiredCodes", "RelativePath": "api/v1/verification/cleanup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiVerificationController", "Method": "DeleteVerificationCode", "RelativePath": "api/v1/verification/delete", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AdminPanel.Areas.V1.Controllers.DeleteVerificationCodeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiVerificationController", "Method": "GetVerificationCode", "RelativePath": "api/v1/verification/get", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AdminPanel.Areas.V1.Controllers.GetVerificationCodeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiVerificationController", "Method": "StoreVerificationCode", "RelativePath": "api/v1/verification/store", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AdminPanel.Areas.V1.Controllers.StoreVerificationCodeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "AdminPanel.Areas.V1.Controllers.ApiVerificationController", "Method": "VerifyCode", "RelativePath": "api/v1/verification/verify", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "AdminPanel.Areas.V1.Controllers.VerifyCodeRequest", "IsRequired": true}], "ReturnTypes": []}]