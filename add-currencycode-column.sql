-- Add CurrencyCode column to Orders table
ALTER TABLE Orders
ADD CurrencyCode NVARCHAR(10) NULL;

-- Optional: Add a comment describing the column
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Currency code for the order (e.g., USD, EUR, GBP)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'Orders', 
    @level2type = N'COLUMN', @level2name = N'CurrencyCode';

-- Optional: Set a default value for existing orders (if needed)
-- UPDATE Orders SET CurrencyCode = 'USD' WHERE CurrencyCode IS NULL;