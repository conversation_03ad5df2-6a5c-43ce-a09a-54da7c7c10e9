'use client';

import React, { useState } from 'react';
import { useCoupon } from '@/contexts/coupon-context';
import { useCart } from '@/contexts/cart-context';

export default function TestCouponValidation() {
  const [couponCode, setCouponCode] = useState('');
  const [validationResult, setValidationResult] = useState<any>(null);
  const { validateCoupon, isLoading } = useCoupon();
  const { items, total } = useCart();

  const handleValidateCoupon = async () => {
    if (!couponCode.trim()) {
      alert('Please enter a coupon code');
      return;
    }

    try {
      const result = await validateCoupon(couponCode, total, items);
      setValidationResult(result);
    } catch (error) {
      console.error('Error validating coupon:', error);
      setValidationResult({
        valid: false,
        message: 'Error validating coupon',
        discount: 0
      });
    }
  };

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Test Coupon Validation</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-lg font-semibold mb-4">Coupon Validation Test</h2>
        
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Coupon Code:</label>
          <input
            type="text"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
            placeholder="Enter coupon code"
          />
        </div>
        
        <button
          onClick={handleValidateCoupon}
          disabled={isLoading}
          className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50"
        >
          {isLoading ? 'Validating...' : 'Validate Coupon'}
        </button>
        
        {validationResult && (
          <div className={`mt-4 p-4 rounded-md ${
            validationResult.valid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            <h3 className="font-semibold">
              {validationResult.valid ? 'Valid Coupon' : 'Invalid Coupon'}
            </h3>
            <p>{validationResult.message}</p>
            {validationResult.valid && (
              <p>Discount: ${validationResult.discount.toFixed(2)}</p>
            )}
          </div>
        )}
        
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2">Cart Information</h3>
          <p>Total Items: {items.length}</p>
          <p>Cart Total: ${total.toFixed(2)}</p>
          <div className="mt-2">
            <h4 className="font-medium">Cart Items:</h4>
            {items.length > 0 ? (
              <ul className="list-disc list-inside">
                {items.map((item) => (
                  <li key={item.id}>
                    Product ID: {item.id}, Name: {item.name}, Quantity: {item.quantity}
                  </li>
                ))}
              </ul>
            ) : (
              <p>No items in cart</p>
            )}
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-gray-100 rounded-md">
          <h3 className="text-lg font-semibold mb-2">Validation Rules Implemented</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li><strong>DiscountTypeId = 1:</strong> Validates OrderCount against MaxQuantity</li>
            <li><strong>DiscountTypeId = 3:</strong> Validates OrderItemsCount against MaxQuantity</li>
            <li><strong>DiscountTypeId = 3:</strong> Checks if cart contains required ProductIds</li>
            <li>All validation messages are user-friendly and descriptive</li>
          </ul>
        </div>
      </div>
    </div>
  );
}