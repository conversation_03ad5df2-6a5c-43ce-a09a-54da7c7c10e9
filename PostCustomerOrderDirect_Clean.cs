[Route("post-order-direct")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> PostCustomerOrderDirect([FromBody] PostCustomerOrderRequest request)
        {
            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;
            result.ActionType = ActionTypeEnum.JSON;
            try
            {
                // Get user ID from JWT token (automatically extracted by middleware)
                int? authenticatedUserId = this.GetCurrentUserIdAsInt();
                // Use authenticated user ID if available, otherwise fall back to request parameter
                int effectiveUserId = authenticatedUserId ?? request.UserID;
                // Validate user ID
                if (effectiveUserId <= 0)
                    throw new ArgumentException("User authentication required - invalid or missing user ID");
                // If both token and request specify user ID, ensure they match for security
                if (authenticatedUserId.HasValue && request.UserID > 0 && authenticatedUserId.Value != request.UserID)
                    throw new UnauthorizedAccessException("Token user ID does not match request user ID");
                // Update request with authenticated user ID
                request.UserID = effectiveUserId;
                Console.WriteLine($"Order placement - Authenticated User ID: {authenticatedUserId}, Effective User ID: {effectiveUserId}");

                if (string.IsNullOrEmpty(request.cartJsonData))
                    throw new ArgumentException("Cart data is required");
                if (request.OrderTotal <= 0)
                    throw new ArgumentException("Order total must be greater than 0");

                // Parse cart data
                var cartItems = JsonConvert.DeserializeObject<List<CustomerFinalOrderItemData>>(request.cartJsonData);
                if (cartItems == null || !cartItems.Any())
                    throw new ArgumentException("Cart cannot be empty");

                // Get user's shipping address ID
                var userAddressId = request.addressid;

                using (var repo = _contextHelper.GetDataContextHelper())
                {
                    int orderId = 0;
                    string orderNumber = "";

                    // Begin transaction for data consistency using PetaPoco's transaction management
                    using (var transaction = repo.GetTransaction())
                    {
                        try
                        {
                            Console.WriteLine("Starting order creation process");

                            // FIXED: Get exchange rate from AppConfigs using your exact SQL query
                            decimal exchangeRate = 1;
                            try
                            {
                                var exchangeRateQuery = @"
                                    DECLARE @ExchangeRate DECIMAL(18,6);
                                    SELECT @ExchangeRate = TRY_CAST(AppConfigValue AS DECIMAL(18,6))
                                    FROM AppConfigs WHERE AppConfigKey = 'currencyprice';
                                    IF @ExchangeRate IS NULL SET @ExchangeRate = 1;
                                    SELECT @ExchangeRate AS ExchangeRate";
                                exchangeRate = repo.ExecuteScalar<decimal>(exchangeRateQuery);
                                Console.WriteLine($"Exchange rate retrieved: {exchangeRate}");
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error getting exchange rate, using default 1: {ex.Message}");
                                exchangeRate = 1;
                            }

                            // Get user's current point balance (handle missing Pointno column gracefully)
                            decimal currentUserPoints = 0;
                            bool pointsColumnExists = true;
                            try
                            {
                                currentUserPoints = repo.ExecuteScalar<decimal?>("SELECT Pointno FROM Users WHERE UserId = @0", request.UserID) ?? 0;
                                Console.WriteLine($"User current points: {currentUserPoints}");
                            }
                            catch (Exception ex) when (ex.Message.Contains("Invalid column name 'Pointno'"))
                            {
                                Console.WriteLine("Pointno column not found in Users table, defaulting to 0 points for user " + request.UserID);
                                currentUserPoints = 0;
                                pointsColumnExists = false;
                            }

                            // Calculate points to deduct (if points are being used)
                            var pointsToUse = request.Point ?? 0;
                            if (pointsToUse > 0)
                            {
                                if (!pointsColumnExists)
                                {
                                    Console.WriteLine("Cannot use points - Pointno column not found. Proceeding with order without point deduction.");
                                }
                                else if (currentUserPoints < pointsToUse)
                                {
                                    throw new Exception($"Insufficient points. User has {currentUserPoints} points but trying to use {pointsToUse} points.");
                                }
                                else
                                {
                                    // Deduct points from user's balance
                                    try
                                    {
                                        var newPointBalance = currentUserPoints - pointsToUse;
                                        repo.Execute("UPDATE Users SET Pointno = @0 WHERE UserId = @1", newPointBalance, request.UserID);
                                        Console.WriteLine($"Points deducted. New balance: {newPointBalance}");
                                    }
                                    catch (Exception ex) when (ex.Message.Contains("Invalid column name 'Pointno'"))
                                    {
                                        Console.WriteLine("Cannot update points - Pointno column not found for user " + request.UserID);
                                    }
                                }
                            }

                            // Get "Active" order status ID
                            var activeStatusId = repo.ExecuteScalar<int?>("SELECT TOP 1 StatusId FROM OrderStatuses WHERE StatusName = 'Active'") ?? 1;
                            Console.WriteLine($"Active status ID: {activeStatusId}");

                            // FIXED: Insert order with proper ExchangeRate and OrderTotal
                            Console.WriteLine($"Inserting order with ExchangeRate: {exchangeRate}, OrderTotal: {request.OrderTotal}");
                            orderId = repo.ExecuteScalar<int>(@"INSERT INTO Orders (CustomerId, OrderDateUtc, ShippingAddressId, OrderTotal, Point, LatestStatusId, ExchangeRate,
                                                         OrderTotalDiscountAmount, OrderTotalShippingCharges, OrderTotalAttributeCharges, OrderTax)
                                                         OUTPUT INSERTED.OrderId
                                                         VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10)",
                                request.UserID, DateTime.UtcNow, userAddressId, request.OrderTotal, request.Point ?? (object)DBNull.Value, activeStatusId, exchangeRate,
                                0, 0, 0, 0);
                            
                            // Verify the order was inserted with correct ExchangeRate
                            var insertedExchangeRate = repo.ExecuteScalar<decimal?>("SELECT ExchangeRate FROM Orders WHERE OrderId = @0", orderId);
                            Console.WriteLine($"Order inserted with ID: {orderId}, Verified ExchangeRate: {insertedExchangeRate}");

                            // Generate order number and update the order
                            orderNumber = $"OR#{orderId:00000000}";
                            repo.Execute("UPDATE Orders SET OrderNumber = @0 WHERE OrderId = @1", orderNumber, orderId);
                            Console.WriteLine($"Order number updated: {orderNumber}");

                            // Add order status mapping entry
                            repo.Execute(@"INSERT INTO OrderStatusesMapping (OrderId, StatusId, IsActive, CreatedOn, CreatedBy)
                                   VALUES (@0, @1, @2, @3, @4)",
                                orderId, activeStatusId, true, DateTime.Now, request.UserID);

                            // FIXED: Add order note if provided with proper error handling
                            if (!string.IsNullOrEmpty(request.OrderNote))
                            {
                                try
                                {
                                    Console.WriteLine($"Attempting to insert order note for OrderId: {orderId}");
                                    repo.Execute(@"INSERT INTO OrderNotes (OrderID, Message, CreatedBy, CreatedOn)
                                          VALUES (@0, @1, @2, @3)",
                                        orderId, request.OrderNote, request.UserID, DateTime.Now);
                                    
                                    // Verify the note was inserted
                                    var noteCount = repo.ExecuteScalar<int>("SELECT COUNT(*) FROM OrderNotes WHERE OrderID = @0", orderId);
                                    Console.WriteLine($"Order note inserted successfully. Total notes for order: {noteCount}");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error inserting order note: {ex.Message}");
                                    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                                }
                            }
                            else
                            {
                                Console.WriteLine("No order note provided to insert");
                            }

                            Console.WriteLine($"Order ID: {orderId}");
                            Console.WriteLine("Processing order items");
                            decimal calculatedTotal = 0;

                            // Process each cart item
                            foreach (var cartItem in cartItems)
                            {
                                // Add debugging for OrderItemAttributeChargesTotal
                                Console.WriteLine($"🔍 CartItem {cartItem.ProductId}: OrderItemAttributeChargesTotal = {cartItem.OrderItemAttributeChargesTotal}");
                                
                                // If OrderItemAttributeChargesTotal is 0, try to calculate it from product attributes
                                if (cartItem.OrderItemAttributeChargesTotal == 0)
                                {
                                    Console.WriteLine($"⚠️ OrderItemAttributeChargesTotal is 0 for Product {cartItem.ProductId}, attempting to calculate...");
                                    
                                    // Calculate attribute charges if not already calculated
                                    decimal calculatedAttributeCharges = 0;
                                    
                                    // Get product attributes for this product
                                    var productAttributes = repo.Query<dynamic>(@"
                                        SELECT pa.ProductAttributeID, pa.AttributeName, pav.PrimaryKeyValue, pav.AttributeValue, pav.AdditionalPrice
                                        FROM ProductAttributes pa
                                        INNER JOIN ProductAttributeValues pav ON pa.ProductAttributeID = pav.ProductAttributeID
                                        WHERE pa.ProductID = @0 AND pav.AdditionalPrice > 0", cartItem.ProductId);
                                    
                                    if (productAttributes.Any())
                                    {
                                        // For now, sum all additional prices (you may need to modify this based on selected attributes)
                                        calculatedAttributeCharges = productAttributes.Sum(attr => Convert.ToDecimal(attr.AdditionalPrice ?? 0)) * cartItem.Quantity;
                                        Console.WriteLine($"📊 Calculated attribute charges for Product {cartItem.ProductId}: {calculatedAttributeCharges}");
                                        
                                        // Update the cart item with calculated charges
                                        cartItem.OrderItemAttributeChargesTotal = calculatedAttributeCharges;
                                    }
                                }
                                
                                // Calculate Order Item Total = (Price * Quantity) - Discount - Point (handle missing Pointno gracefully)
                                decimal cartItemPoints = 0;
                                try
                                {
                                    cartItemPoints = cartItem.Pointno ?? 0;
                                }
                                catch
                                {
                                    cartItemPoints = 0; // Default to 0 if Pointno property doesn't exist
                                }

                                // Use ItemSubTotal if non-zero, otherwise calculate
                                decimal itemTotal = cartItem.ItemSubTotal != 0 ? cartItem.ItemSubTotal :
                                    ((cartItem.Price * cartItem.Quantity) - (cartItem.OrderItemDiscountTotal ?? 0) - cartItemPoints);

                                Console.WriteLine($"Item {cartItem.ProductId}: Price={cartItem.Price}, Qty={cartItem.Quantity}, Discount={cartItem.OrderItemDiscountTotal ?? 0}, Points={cartItemPoints}, Total={itemTotal}");

                                // Generate OrderItemGuid for this order item
                                var orderItemGuid = Guid.NewGuid();
                                int orderItemId = 0;

                                // Get vendor commission ID
                                int? vendorId = repo.ExecuteScalar<int?>("SELECT TOP 1 VendorID FROM Products WHERE ProductID = @0", cartItem.ProductId);
                                int? vendorCommissionId = null;

                                if (vendorId.HasValue)
                                {
                                    vendorCommissionId = repo.ExecuteScalar<int?>(
                                        @"SELECT TOP 1 VendorCommissionID 
                                  FROM VendorsCommissionSetup 
                                  WHERE UserID = @0 AND IsActive = 1 
                                  AND (CAST(GETDATE() AS DATE) BETWEEN CAST(ApplicableFrom AS DATE) AND CAST(ApplicableTo AS DATE))",
                                        vendorId.Value);
                                    Console.WriteLine($"Vendor commission found for ProductID {cartItem.ProductId}: VendorCommissionID={vendorCommissionId}");
                                }

                                // Insert order item using direct SQL with additional fields
                                try
                                {
                                    orderItemId = repo.ExecuteScalar<int>(@"INSERT INTO OrderItems (OrderId, ProductId, Quantity, Price, ItemPriceTotal,
                                         OrderItemDiscountTotal, OrderItemShippingChargesTotal, OrderItemAttributeChargesTotal,
                                         DiscountId, OrderItemTotal, Pointno, OrderItemGuid, OrderItemTaxTotal, VendorCommissionID)
                                         OUTPUT INSERTED.OrderItemID
                                         VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10, @11, @12, @13)",
                                        orderId, cartItem.ProductId, cartItem.Quantity, cartItem.Price, cartItem.ItemPriceTotal,
                                        cartItem.OrderItemDiscountTotal ?? 0, cartItem.ShippingChargesTotal, cartItem.OrderItemAttributeChargesTotal,
                                        cartItem.DiscountId ?? 0, itemTotal, cartItemPoints, orderItemGuid, 0, vendorCommissionId ?? (object)DBNull.Value);
                                }
                                catch (Exception ex) when (ex.Message.Contains("Invalid column name"))
                                {
                                    // Handle missing columns gracefully - fallback to basic insertion
                                    Console.WriteLine($"Some columns not found in OrderItems table: {ex.Message}");
                                    orderItemId = repo.ExecuteScalar<int>(@"INSERT INTO OrderItems (OrderId, ProductId, Quantity, Price, ItemPriceTotal,
                                             OrderItemDiscountTotal, OrderItemShippingChargesTotal, OrderItemAttributeChargesTotal,
                                             DiscountId, OrderItemTotal, OrderItemGuid, OrderItemTaxTotal)
                                             OUTPUT INSERTED.OrderItemID
                                             VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10, @11)",
                                        orderId, cartItem.ProductId, cartItem.Quantity, cartItem.Price, cartItem.ItemPriceTotal,
                                        cartItem.OrderItemDiscountTotal ?? 0, cartItem.ShippingChargesTotal, cartItem.OrderItemAttributeChargesTotal,
                                        cartItem.DiscountId ?? 0, itemTotal, orderItemGuid, 0);
                                }

                                calculatedTotal += itemTotal;

                                // Update product stock quantity
                                int newStockQuantity = repo.ExecuteScalar<int>("SELECT ISNULL(StockQuantity, 0) - @0 FROM Products WHERE ProductID = @1",
                                    cartItem.Quantity, cartItem.ProductId);
                                repo.Execute("UPDATE Products SET StockQuantity = @0 WHERE ProductID = @1",
                                    newStockQuantity, cartItem.ProductId);
                                Console.WriteLine($"Product stock updated for ProductID {cartItem.ProductId}: New quantity = {newStockQuantity}");

                                // Insert into discount usage history if discount was used
                                if (cartItem.DiscountId.HasValue && cartItem.DiscountId > 0)
                                {
                                    repo.Execute(@"INSERT INTO DiscountUsageHistory(DiscountID, UsedBy, UsageDate)
                                           VALUES (@0, @1, @2)",
                                        cartItem.DiscountId.Value, request.UserID, DateTime.Now);
                                    Console.WriteLine($"Discount usage recorded for DiscountID {cartItem.DiscountId.Value}");
                                }

                                // FIXED: Insert OrderProductAttributeMapping records for product attributes/options
                                try
                                {
                                    if (!string.IsNullOrEmpty(cartItem.ProductAllSelectedAttributes))
                                    {
                                        var selectedAttributes = JsonConvert.DeserializeObject<List<dynamic>>(cartItem.ProductAllSelectedAttributes);
                                        if (selectedAttributes != null && selectedAttributes.Any())
                                        {
                                            Console.WriteLine($"Processing {selectedAttributes.Count} attributes for OrderItem {orderItemId}");
                                            foreach (var attribute in selectedAttributes)
                                            {
                                                try
                                                {
                                                    var productAttributeId = Convert.ToInt32(attribute.ProductAttributeID ?? 0);
                                                    var attributeValueId = Convert.ToInt32(attribute.AttributeValueID ?? 0);
                                                    var additionalPrice = Convert.ToDecimal(attribute.PriceAdjustment ?? 0);
                                                    var attributeValueText = attribute.AttributeValueText?.ToString() ?? "";

                                                    if (productAttributeId > 0 && attributeValueId > 0)
                                                    {
                                                        repo.Execute(@"INSERT INTO OrderProductAttributeMapping (ProductAttributeID, OrderItemID, AttributeValue, AttrAdditionalPrice)
                                                              VALUES (@0, @1, @2, @3)",
                                                            productAttributeId, orderItemId, attributeValueId, additionalPrice);
                                                        Console.WriteLine($"OrderProductAttributeMapping created: ProductAttributeID={productAttributeId}, AttributeValueID={attributeValueId}, Price={additionalPrice}");
                                                    }
                                                    else
                                                    {
                                                        Console.WriteLine($"Skipping attribute with invalid IDs: ProductAttributeID={productAttributeId}, AttributeValueID={attributeValueId}");
                                                    }
                                                }
                                                catch (Exception attrEx)
                                                {
                                                    Console.WriteLine($"Error processing individual attribute: {attrEx.Message}");
                                                }
                                            }
                                        }
                                        else
                                        {
                                            Console.WriteLine($"No valid attributes found for OrderItem {orderItemId}");
                                        }
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error creating OrderProductAttributeMapping: {ex.Message}");
                                }
                            }

                            // Calculate and update detailed order totals
                            var orderTotalsQuery = repo.Query<dynamic>(@"SELECT 
                        SUM(ISNULL(OrderItemShippingChargesTotal,0)) AS ShippingCharges,
                        SUM(ISNULL(OrderItemTaxTotal,0)) AS Tax,
                        SUM(ISNULL(OrderItemDiscountTotal,0)) AS DiscountAmount,
                        SUM(ISNULL(OrderItemAttributeChargesTotal,0)) AS AttributeCharges,
                        SUM(ISNULL(OrderItemTotal,0)) AS Total
                        FROM OrderItems WHERE OrderID = @0", orderId).FirstOrDefault();

                            decimal shippingCharges = 0, tax = 0, discountAmount = 0, attributeCharges = 0, total = 0;

                            if (orderTotalsQuery != null)
                            {
                                var totalsDict = orderTotalsQuery as IDictionary<string, object>;
                                if (totalsDict != null)
                                {
                                    shippingCharges = Convert.ToDecimal(totalsDict["ShippingCharges"] ?? 0);
                                    tax = Convert.ToDecimal(totalsDict["Tax"] ?? 0);
                                    discountAmount = Convert.ToDecimal(totalsDict["DiscountAmount"] ?? 0);
                                    attributeCharges = Convert.ToDecimal(totalsDict["AttributeCharges"] ?? 0);
                                    total = Convert.ToDecimal(totalsDict["Total"] ?? 0);
                                }
                            }

                            repo.Execute(@"UPDATE Orders SET 
                        OrderNumber = @0,
                        OrderTotalShippingCharges = @1,
                        OrderTax = @2,
                        OrderTotalDiscountAmount = @3,
                        OrderTotalAttributeCharges = @4,
                        OrderTotal = @5
                        WHERE OrderID = @6",
                                orderNumber, shippingCharges, tax, discountAmount, attributeCharges, total, orderId);

                            Console.WriteLine($"Order totals updated: Shipping={shippingCharges}, Tax={tax}, " +
                                             $"Discount={discountAmount}, Attributes={attributeCharges}, Total={total}");

                            // FIXED: Insert OrderShippingDetail records for all order items
                            var orderItems = repo.Query<int>("SELECT OrderItemID FROM OrderItems WHERE OrderID = @0", orderId);
                            Console.WriteLine($"Found {orderItems.Count()} order items to create shipping details for");
                            
                            foreach (var orderItemId in orderItems)
                            {
                                try
                                {
                                    Console.WriteLine($"Inserting OrderShippingDetail for OrderItem {orderItemId}, OrderID {orderId}, StatusID {activeStatusId}");
                                    repo.Execute(@"INSERT INTO OrderShippingDetail (OrderID, OrderItemID, ShippingStatusID)
                                          VALUES (@0, @1, @2)",
                                        orderId, orderItemId, activeStatusId);
                                    Console.WriteLine($"OrderShippingDetail created successfully for OrderItem {orderItemId}");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error creating OrderShippingDetail for OrderItem {orderItemId}: {ex.Message}");
                                    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                                }
                            }
                            
                            // Verify shipping details were created
                            var shippingDetailCount = repo.ExecuteScalar<int>("SELECT COUNT(*) FROM OrderShippingDetail WHERE OrderID = @0", orderId);
                            Console.WriteLine($"Total OrderShippingDetail records created: {shippingDetailCount}");

                            // Create payment record
                            int currencyId = 1;
                            if (!string.IsNullOrEmpty(request.CurrencyCode))
                            {
                                currencyId = repo.ExecuteScalar<int?>(
                                    "SELECT TOP 1 CurrencyID FROM Currencies WHERE CurrencyCode = @0", request.CurrencyCode) ?? 1;
                                Console.WriteLine($"Currency found: Code={request.CurrencyCode}, ID={currencyId}");
                            }

                            string milestoneName = "Milestone 1";
                            bool isCompleted = true;
                            var finalOrderTotal = repo.ExecuteScalar<decimal>("SELECT TOP 1 OrderTotal FROM Orders WHERE OrderID = @0", orderId);

                            repo.Execute(@"INSERT INTO OrdersPayments (OrderId, PaymentMethodId, MilestoneValue, MilestoneName,
                                 CurrencyId, IsCompleted, PaymentDate)
                                 VALUES (@0, @1, @2, @3, @4, @5, @6)",
                                orderId, request.PaymentMethod, finalOrderTotal, milestoneName, currencyId, isCompleted, DateTime.Now);
                            Console.WriteLine($"Payment record created for order {orderId} with CurrencyID={currencyId}");

                            // Create notification
                            try
                            {
                                var firstName = repo.ExecuteScalar<string>("SELECT TOP 1 FirstName FROM Users WHERE UserID = @0", request.UserID) ?? "Customer";
                                var notificationMessage = $"New order {orderNumber} has been placed by {firstName} at {DateTime.Now.Date:yyyy-MM-dd}";

                                try
                                {
                                    repo.Execute("EXEC [dbo].[SP_InsertAdminPanelNotification] @0, @1, @2, @3",
                                        "New Order Placed", notificationMessage, 1, "");
                                    Console.WriteLine("Notification created using stored procedure");
                                }
                                catch
                                {
                                    var notificationId = repo.ExecuteScalar<int>(@"INSERT INTO AdminPanelNotifications (Title, Message, NotificationTypeId, IsRead, ClickUrl, CreatedOn)
                                                          OUTPUT INSERTED.NotificationId
                                                          VALUES (@0, @1, @2, @3, @4, @5)",
                                        "New Order Placed", notificationMessage, 1, false, "", DateTime.Now);
                                    Console.WriteLine($"Notification created with ID: {notificationId}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error creating notification: {ex.Message}");
                            }

                            // Commit transaction
                            transaction.Complete();
                            Console.WriteLine("Order creation completed successfully");

                            result.Data = new
                            {
                                OrderID = orderId,
                                OrderNumber = orderNumber,
                                Message = "Order Placed Successfully"
                            };
                            result.StatusCode = 200;
                            result.StatusMessage = "Ok";
                            result.ErrorMessage = string.Empty;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error in order creation: {ex.Message}");
                            // Transaction will be automatically rolled back when disposed
                            throw;
                        }
                    }
                }
                await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                result.Data = null;
                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = ex.Message;
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
            }
            apiActionResult = new APIActionResult(result);
            return apiActionResult;
        }