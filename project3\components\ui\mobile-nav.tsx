'use client';

import { Home, User, ShoppingCart, Heart, Grid3X3 } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { useSettings } from '@/contexts/settings-context';
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MobileCategoriesNew } from './mobile-categories-new';

export function MobileNav() {
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();
  const { totalItems: cartCount } = useCart();
  const { totalItems: wishlistCount } = useWishlist();
  const { primaryColor, t } = useSettings();
  const [showCategories, setShowCategories] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const navItems = [
    {
      href: '/',
      icon: Home,
      label: t?.('home') || 'الرئيسية',
      isActive: pathname === '/',
    },
    {
      icon: Grid3X3,
      label: t?.('categories') || 'التصنيفات',
      onClick: () => setShowCategories(true),
    },
    {
      href: '/cart',
      icon: ShoppingCart,
      label: t?.('cart') || 'سلة التسوق',
      isActive: pathname === '/cart',
      badge: cartCount || 0,
    },
    {
      href: '/wishlist',
      icon: Heart,
      label: t?.('wishlist') || 'المفضلة',
      isActive: pathname === '/wishlist',
      badge: wishlistCount || 0,
    },
    {
      href: '/login',
      icon: User,
      label: t?.('login') || 'حسابي',
      isActive: pathname === '/login' || pathname === '/signup',
    },
  ];

  return (
    <>
      {/* Bottom Navigation */}
      <nav className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-100 z-40 safe-area-pb">
        <div className="flex items-center justify-end py-2 px-4 md:px-6">
          {navItems.map((item, index) => {
            const Icon = item.icon;
            const isActive = item.isActive || false;
            const badge = item.badge || 0;

            return item.href ? (
              <Link
                key={index}
                href={item.href}
                className={`flex flex-col items-center justify-center py-3 px-3 ml-2 md:ml-4 md:px-4 ${
                  isActive ? 'text-blue-600' : 'text-gray-500'
                }`}
              >
                <div className="relative">
                  <Icon className="w-6 h-6 md:w-7 md:h-7 mx-auto" />
                  {badge > 0 && (
                    <span 
                      className="absolute -top-1 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 md:h-6 md:w-6 flex items-center justify-center"
                      style={{ minWidth: '1.25rem' }}
                    >
                      {badge > 99 ? '99+' : badge}
                    </span>
                  )}
                </div>
                <span className="text-xs md:text-sm mt-1">{item.label}</span>
              </Link>
            ) : (
              <button
                key={index}
                onClick={item.onClick}
                className={`flex flex-col items-center justify-center py-3 px-3 ml-2 ${
                  isActive ? 'text-blue-600' : 'text-gray-500'
                }`}
              >
                <Icon className="w-6 h-6 mx-auto" />
                <span className="text-xs mt-1">{item.label}</span>
              </button>
            );
          })}
        </div>
      </nav>

      {/* Categories Modal */}
      <AnimatePresence>
        {showCategories && (
          <MobileCategoriesNew onClose={() => setShowCategories(false)} />
        )}
      </AnimatePresence>
    </>
  );
}
