@tailwind base;
@tailwind components;
@tailwind utilities;

/* SweetAlert2 styles */
@import 'sweetalert2/dist/sweetalert2.min.css';

/* SweetAlert2 theme customization */
.swal2-popup {
  background: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.swal2-title {
  color: hsl(var(--foreground)) !important;
}

.swal2-content {
  color: hsl(var(--muted-foreground)) !important;
}

.swal2-confirm {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border: none !important;
}

.swal2-cancel {
  background-color: hsl(var(--secondary)) !important;
  color: hsl(var(--secondary-foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.swal2-confirm:hover {
  background-color: hsl(var(--primary)) !important;
  opacity: 0.9;
}

.swal2-cancel:hover {
  background-color: hsl(var(--accent)) !important;
}

/* Mobile safe area support */
.safe-area-pb {
  padding-bottom: env(safe-area-inset-bottom);
}

/* Mobile bottom navigation styles */
@media (max-width: 768px) {
  body {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Hide reCAPTCHA badge */
.grecaptcha-badge {
  visibility: hidden !important;
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
  
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
  }
  
  .scrollbar-track-gray-100::-webkit-scrollbar-track {
    background-color: #f3f4f6;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* React Phone Input 2 - Remove white background from country dropdown */
.react-tel-input .flag-dropdown {
  background: transparent !important;
}

.react-tel-input .country-list {
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

.react-tel-input .country-list .country {
  background: transparent !important;
  color: hsl(var(--foreground)) !important;
}

.react-tel-input .country-list .country:hover {
  background: hsl(var(--accent)) !important;
}

.react-tel-input .country-list .country.highlight {
  background: hsl(var(--accent)) !important;
}

.react-tel-input .search-box {
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border)) !important;
  color: hsl(var(--foreground)) !important;
}

/* React Phone Input 2 styles */
.react-tel-input .search-box:focus {
  border-color: hsl(var(--ring)) !important;
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 2px hsl(var(--ring)) !important;
}

/* Tablet responsiveness fixes for 1024px-1400px range */
@media (min-width: 1024px) and (max-width: 1400px) {
  /* Header improvements for tablets */
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  
  /* Header logo sizing for tablets */
  .header-logo {
    height: 3.5rem; /* 56px - between mobile and desktop */
  }
  
  /* Header search bar improvements */
  .header-search {
    max-width: 20rem; /* 320px - better proportion for tablets */
  }
  
  /* Header navigation spacing */
  .header-nav {
    gap: 1rem; /* Reduce gap between nav items */
  }
  
  /* Footer grid improvements */
  .footer-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  /* Footer text sizing */
  .footer-text {
    font-size: 0.9rem;
    line-height: 1.4;
  }
  
  /* Footer headings */
  .footer-heading {
    font-size: 1.1rem;
    margin-bottom: 1rem;
  }
  
  /* Footer links spacing */
  .footer-links {
    gap: 0.75rem;
  }
  
  /* Newsletter form improvements */
  .newsletter-input {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  /* Social media icons */
  .social-icons {
    gap: 1rem;
  }
}

/* Dialog z-index fixes */
.dialog-overlay-fix {
  z-index: 9990 !important;
}

.dialog-content-fix {
  z-index: 9999 !important;
  background-color: white !important;
  position: relative !important;
}

/* Custom accordion styles */
.address-accordion-trigger {
  transition: all 0.3s ease;
}

.address-accordion-trigger[data-state="open"] {
  background: linear-gradient(to right, #e0f2fe, #e0e7ff);
}

.address-accordion-content {
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

.address-accordion-content[data-state="closed"] {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
  from {
    height: 0;
    opacity: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-accordion-content-height);
    opacity: 1;
  }
  to {
    height: 0;
    opacity: 0;
  }
}
