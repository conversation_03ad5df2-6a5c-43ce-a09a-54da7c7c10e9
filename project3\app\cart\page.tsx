'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useUser } from '@/contexts/user-context';
import { useCart } from '@/contexts/cart-context';
import { useCoupon } from '@/contexts/coupon-context';
import { useCurrency } from '@/contexts/currency-context';
import { ShoppingCart, Trash2, ChevronRight, Plus, Minus, Check, DollarSign, Star, LogIn } from 'lucide-react';
import Swal from 'sweetalert2';

export default function CartPage() {
  const { t, primaryColor } = useSettings();
  const { user, isLoggedIn, refreshCredit } = useUser();
  const { items, removeFromCart, updateQuantity, totalItems, subtotal, subtotalIQD, total, totalIQD } = useCart();
  const { validateCoupon, appliedCoupon, clearCoupon, isLoading } = useCoupon();
  const { formatIQD, formatUSD } = useCurrency();
  const router = useRouter();
  const [couponCode, setCouponCode] = useState('');
  const [showUSD, setShowUSD] = useState(false);
  const [usePoints, setUsePoints] = useState(false);

  // Add useEffect to refresh credit when page loads
  useEffect(() => {
    if (isLoggedIn && user) {
      refreshCredit();
    }
  }, [isLoggedIn, user, refreshCredit]);

  // Save credit usage to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('usePoints', usePoints.toString());
  }, [usePoints]);

  // Redirect to login if not logged in, with cart redirect parameter
  useEffect(() => {
    if (!isLoggedIn) {
      router.push('/login?redirect=/cart');
    }
  }, [isLoggedIn, router]);

  // Calculate credit discount
  const userPoints = user?.Pointno || 0;
  const pointsDiscount = usePoints ? userPoints : 0;
  const pointsDiscountIQD = Math.round(pointsDiscount * 1500); // Convert to IQD

  // Calculate final totals with credit discount
  const finalTotal = Math.max(0, total - (appliedCoupon ? appliedCoupon.discount : 0) - pointsDiscount);
  const finalTotalIQD = Math.max(0, totalIQD - (appliedCoupon ? Math.round(appliedCoupon.discount * 1500) : 0) - pointsDiscountIQD);

  // Show login required message if not logged in
  if (!isLoggedIn) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Card className="max-w-md mx-auto">
          <div className="p-8 text-center">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <LogIn className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-lg font-medium mb-2">Login Required</h3>
            <p className="text-muted-foreground mb-4">Please log in to view your cart and continue shopping</p>
            <Button asChild className="w-full">
              <Link href="/login?redirect=/cart">
                Login to Continue
              </Link>
            </Button>
          </div>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-4 sm:py-6 md:py-8 px-4">
      {/* Breadcrumb */}
      <Breadcrumb className="mb-4 sm:mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">{t('home')}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>{t('cart')}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Content */}
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 gap-2">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold">{t('cart')}</h1>
            {userPoints > 0 && (
              <div className="flex items-center gap-2 mt-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm text-muted-foreground">
                  You have {userPoints} credit ({showUSD ? formatUSD(userPoints) : formatIQD(Math.round(userPoints * 1500))})
                </span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-4">
            <p className="text-muted-foreground">{totalItems} {totalItems === 1 ? 'item' : 'items'}</p>
            <div className="flex items-center gap-2">
              <Button
                variant={showUSD ? "outline" : "default"}
                size="sm"
                onClick={() => setShowUSD(false)}
                className="flex items-center gap-2"
              >
                IQD
              </Button>
              <Button
                variant={showUSD ? "default" : "outline"}
                size="sm"
                onClick={() => setShowUSD(true)}
                className="flex items-center gap-2"
              >
                <DollarSign className="h-4 w-4" />
                USD
              </Button>
            </div>
          </div>
        </div>
        
        {items.length > 0 ? (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2 space-y-4">
              {items.map((item) => (
                <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300 border-l-4" style={{ borderLeftColor: primaryColor }}>
                  <div className="flex flex-col sm:flex-row">
                    <div className="w-full sm:w-40 h-40 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden">
                      <img
                        src={item.image || `/products/book${item.id}.jpg`}
                        alt={item.name}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium" style={{ color: primaryColor }}>
                        #{item.id}
                      </div>
                    </div>
                    
                    <div className="flex-1 p-6 flex flex-col sm:flex-row justify-between bg-gradient-to-r from-white to-gray-50/50">
                      <div className="flex-1">
                        {/* Product Name - First Line */}
                        <h3 className="font-semibold text-lg mb-3 text-gray-800 leading-tight">{item.name}</h3>

                        {/* Price Display - Prominent IQD with USD secondary */}
                        <div className="mb-4 p-3 bg-white rounded-lg border border-gray-100 shadow-sm">
                          {!showUSD ? (
                            // IQD Primary Display
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <div className="text-2xl font-bold" style={{ color: primaryColor }}>
                                  {formatIQD(item.adjustedIqdPrice || item.iqdPrice || 0)}
                                </div>
                                <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                  IQD
                                </div>
                              </div>
                              <div className="text-sm text-muted-foreground flex items-center gap-1">
                                <span>≈</span>
                                <span>{formatUSD(item.adjustedPrice)}</span>
                              </div>
                            </div>
                          ) : (
                            // USD Primary Display
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <div className="text-2xl font-bold" style={{ color: primaryColor }}>
                                  {formatUSD(item.adjustedPrice)}
                                </div>
                                <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                  USD
                                </div>
                              </div>
                              <div className="text-sm text-muted-foreground flex items-center gap-1">
                                <span>≈</span>
                                <span>{formatIQD(item.adjustedIqdPrice || item.iqdPrice || 0)}</span>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Show discount info if applicable */}
                        {item.discountPrice && item.discountPrice < (item.originalPrice || item.price) && (
                          <div className="text-sm text-green-600 mb-2 flex items-center gap-2">
                            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                              SALE
                            </span>
                            <span>
                              Original: {showUSD ? formatUSD(item.originalPrice) : formatIQD(Math.round(item.originalPrice * 1500))}
                            </span>
                          </div>
                        )}
                        {/* Display selected attributes */}
                        {item.attributes && item.attributes.length > 0 && (
                          <div className="mt-2 space-y-1 pt-2 border-t border-gray-100">
                            <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-1">
                              Selected Options:
                            </div>
                            {item.attributes.map((attr, idx) => (
                              <div key={idx} className="flex items-center justify-between text-sm">
                                <span>
                                  <span className="font-medium">{attr.DisplayName || attr.AttributeName}:</span>{' '}
                                  {attr.AttributeValueText}
                                </span>
                                {attr.PriceAdjustment && (
                                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                    {attr.PriceAdjustmentType === 1 ? '+' : ''}
                                    {showUSD
                                      ? `$${attr.PriceAdjustment}`
                                      : `${Math.round(attr.PriceAdjustment * 1500).toLocaleString()} IQD`
                                    }
                                    {attr.PriceAdjustmentType === 2 ? '%' : ''}
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-4 mt-4 sm:mt-0">
                        <div className="flex items-center bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                          <button
                            className="p-3 hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            <Minus className="h-4 w-4" />
                          </button>
                          <span className="px-4 py-3 font-medium bg-white border-x border-gray-200 min-w-[3rem] text-center">{item.quantity}</span>
                          <button
                            className="p-3 hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            <Plus className="h-4 w-4" />
                          </button>
                        </div>

                        <button
                          className="p-3 text-red-500 hover:bg-red-50 rounded-lg transition-colors border border-red-200 hover:border-red-300"
                          onClick={() => removeFromCart(item.id)}
                          title="Remove from cart"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
            
            {/* Order Summary */}
            <div className="lg:col-span-1">
              <Card className="sticky top-4 shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h2 className="text-xl font-bold text-gray-800 flex items-center gap-2">
                      <div className="w-2 h-6 rounded-full" style={{ backgroundColor: primaryColor }}></div>
                      Order Summary
                    </h2>
                    <div className="flex items-center gap-1">
                      <Button
                        variant={showUSD ? "outline" : "default"}
                        size="sm"
                        onClick={() => setShowUSD(false)}
                        className="text-xs border-2 hover:scale-105 transition-transform px-2"
                        style={{ 
                          borderColor: primaryColor, 
                          backgroundColor: !showUSD ? primaryColor : 'transparent',
                          color: !showUSD ? 'white' : primaryColor 
                        }}
                      >
                        IQD
                      </Button>
                      <Button
                        variant={showUSD ? "default" : "outline"}
                        size="sm"
                        onClick={() => setShowUSD(true)}
                        className="text-xs border-2 hover:scale-105 transition-transform px-2"
                        style={{ 
                          borderColor: primaryColor, 
                          backgroundColor: showUSD ? primaryColor : 'transparent',
                          color: showUSD ? 'white' : primaryColor 
                        }}
                      >
                        USD
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-4 mb-6">
                    {/* Subtotal */}
                    <div className="flex justify-between items-center">
                      <span className="text-muted-foreground">Subtotal</span>
                      <div className="text-right">
                        <div className="font-medium">
                          {showUSD ? formatUSD(subtotal) : formatIQD(subtotalIQD)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          ≈ {showUSD ? formatIQD(subtotalIQD) : formatUSD(subtotal)}
                        </div>
                      </div>
                    </div>



                    {/* Credit Discount */}
                    {userPoints > 0 && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Checkbox
                              id="use-points"
                              checked={usePoints}
                              onCheckedChange={(checked) => setUsePoints(checked as boolean)}
                            />
                            <label htmlFor="use-points" className="flex items-center gap-2 cursor-pointer">
                              <Star className="h-4 w-4 text-yellow-600" />
                              <span className="font-medium text-yellow-700">Use Credit</span>
                            </label>
                          </div>
                          {usePoints && (
                            <span className="text-yellow-700 font-bold">
                              -{showUSD ? formatUSD(pointsDiscount) : formatIQD(pointsDiscountIQD)}
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-yellow-600">
                          Available: {userPoints} credit = {showUSD ? formatUSD(userPoints) : formatIQD(Math.round(userPoints * 1500))}
                        </div>
                        {usePoints && (
                          <div className="text-xs text-yellow-600 mt-1">
                            ✓ Using {userPoints} credit as discount
                          </div>
                        )}
                      </div>
                    )}

                    {/* Coupon Discount */}
                    {appliedCoupon && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3 space-y-2">
                        <div className="flex justify-between items-center">
                          <div className="flex flex-col">
                            <span className="text-green-700 font-medium">
                              {appliedCoupon.title || appliedCoupon.code}
                            </span>
                            <span className="text-xs text-green-600">
                              Code: {appliedCoupon.code} • 
                              {appliedCoupon.discountTypeId === 1 && 'Applied on order total'}
                              {appliedCoupon.discountTypeId === 2 && 'Applied on order subtotal'}
                              {appliedCoupon.discountTypeId === 3 && 'Applied on products'}
                              {appliedCoupon.discountTypeId === 4 && 'Applied on categories'}
                              {appliedCoupon.discountTypeId === 5 && 'Applied on manufacturers'}
                              {appliedCoupon.discountTypeId === 6 && 'Applied on cities'}
                              {appliedCoupon.discountTypeId === 7 && 'Applied on shipping'}
                            </span>
                          </div>
                          <span className="text-green-700 font-bold">
                            -{showUSD ? formatUSD(appliedCoupon.discount) : formatIQD(Math.round(appliedCoupon.discount * 1500))}
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Total */}
                    <div className="border-t pt-4 mt-4">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold">Total</span>
                        <div className="text-right">
                          <div className="text-xl font-bold" style={{ color: primaryColor }}>
                            {showUSD ? formatUSD(finalTotal) : formatIQD(finalTotalIQD)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            ≈ {showUSD ? formatIQD(finalTotalIQD) : formatUSD(finalTotal)}
                          </div>
                        </div>
                      </div>
                      
                      {/* Show savings summary if any discounts applied */}
                      {(appliedCoupon || usePoints) && (
                        <div className="mt-3 p-3 bg-green-50 rounded-lg border border-green-200">
                          <div className="text-sm text-green-700">
                            <div className="font-medium mb-1">💰 You're saving:</div>
                            {appliedCoupon && (
                              <div className="flex justify-between">
                                <span>Coupon discount:</span>
                                <span>-{showUSD ? formatUSD(appliedCoupon.discount) : formatIQD(Math.round(appliedCoupon.discount * 1500))}</span>
                              </div>
                            )}
                            {usePoints && (
                              <div className="flex justify-between">
                                <span>Credit discount:</span>
                                <span>-${pointsDiscount.toFixed(2)}</span>
                              </div>
                            )}
                            <div className="border-t border-green-300 mt-2 pt-2 flex justify-between font-bold">
                              <span>Total savings:</span>
                              <span>
                                -{showUSD 
                                  ? formatUSD((appliedCoupon ? appliedCoupon.discount : 0) + pointsDiscount)
                                  : formatIQD((appliedCoupon ? Math.round(appliedCoupon.discount * 1500) : 0) + pointsDiscountIQD)
                                }
                              </span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="mt-4">
                      <div className="flex gap-2">
                        <input
                          type="text"
                          placeholder="Enter coupon code"
                          className="flex-1 p-2 border rounded-md"
                          value={couponCode}
                          onChange={(e) => setCouponCode(e.target.value)}
                        />
                        <Button
                          onClick={async () => {
                            if (!couponCode) return;

                            try {
                              const result = await validateCoupon(couponCode, subtotal, items);
                              if (result.valid) {
                                Swal.fire({
                                  title: 'Success!',
                                  text: result.message,
                                  icon: 'success',
                                  timer: 2000,
                                  showConfirmButton: false
                                });
                                setCouponCode('');
                              } else {
                                Swal.fire({
                                  title: 'Error',
                                  text: result.message,
                                  icon: 'error',
                                  timer: 2000,
                                  showConfirmButton: false
                                });
                              }
                            } catch (error) {
                              Swal.fire({
                                title: 'Error',
                                text: 'Failed to validate coupon. Please try again.',
                                icon: 'error',
                                timer: 2000,
                                showConfirmButton: false
                              });
                            }
                          }}
                          variant="outline"
                          disabled={isLoading}
                        >
                          {isLoading ? 'Validating...' : 'Apply'}
                        </Button>
                      </div>
                      {appliedCoupon && (
                        <div className="flex items-center justify-between mt-2 text-sm">
                          <span className="text-green-600">Coupon {appliedCoupon.code} applied</span>
                          <button
                            onClick={() => {
                              clearCoupon();
                              Swal.fire({
                                title: 'Coupon Removed',
                                text: 'Coupon has been removed successfully',
                                icon: 'info',
                                timer: 2000,
                                showConfirmButton: false
                              });
                            }}
                            className="text-red-500 hover:underline"
                          >
                            Remove
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <Button 
                    className="w-full py-6"
                    style={{ backgroundColor: primaryColor }}
                    asChild
                  >
                    <Link href="/checkout">
                      Proceed to Checkout
                    </Link>
                  </Button>
                  
                  <div className="mt-4">
                    <Link 
                      href="/" 
                      className="text-sm text-center block hover:underline"
                    >
                      Continue Shopping
                    </Link>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        ) : (
          <Card>
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <ShoppingCart className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">Your cart is empty</h3>
              <p className="text-muted-foreground mb-4">Add items to your cart to proceed to checkout</p>
              <Button asChild>
                <Link href="/">
                  Continue Shopping
                </Link>
              </Button>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}