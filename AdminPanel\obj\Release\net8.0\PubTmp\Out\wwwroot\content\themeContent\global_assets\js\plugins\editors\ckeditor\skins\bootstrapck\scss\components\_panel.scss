/*
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
*/

/*
panel.css (part of editor.css)
================================

Panels are floating elements that can hold different types of contents.
The following are common uses of it:

    - The element that toolbar combos display when opening them.
    - The context menu.
    - The list of items displayed by "menu buttons" (e.g. scayt).
    - The panel shown when opening "panel buttons" (e.g. color buttons).

Panel contents are wrapped into an iframe, so it is possible to have additional
CSS loaded inside them (e.g. to have more accurate preview on the styles combo).

The following is a visual representation of the outer elements of a panel:

+-- .cke_panel(*) ---------------------+
| +-- IFRAME.cke_panel_frame --------+ |
| | +-- HTML.cke_panel_container --+ | |
| | | +-- .cke_panel_block ------+ | | |
| | | |                          | | | |
| | | |     (contents here)      | | | |
| | | |                          | | | |
| | | +--------------------------+ | | |
| | +------------------------------+ | |
| +----------------------------------+ |
+--------------------------------------+

(*) All kinds of panel share the above structure. Menu panels adds the
    .cke_menu_panel class to the outer element, while toolbar combos add the
    .cke_combopanel class.

This file also defines styles for panel lists (used by combos). For menu-like
panel contents and color panels check menu.css and colorpanel.css.
*/

/* The box that holds an IFRAME. It's inserted into a host document and positioned
   absolutely by the application. It floats above the host document/editor. */
.cke_panel {
    /* Restore the loading hide */
    visibility: visible;
    width: 120px;
    height: 100px;
    overflow: hidden;
    margin-top: 5px;

    background-color: #fff;
    border: 1px solid $gray;
    border-radius: $border-radius;
}

/* This class represents panels which are used as context menus. */
.cke_menu_panel {
    padding: 0;
    margin: 0;
}

/* This class represents panels which are used by rich combos. */
.cke_combopanel {
    width: 150px;
    height: 178px;
}

/* The IFRAME the panel is wrapped into. */
.cke_panel_frame {
    width: 100%;
    height: 100%;
    font-size: 12px;

    overflow: auto;
    overflow-x: hidden;
}

/* The HTML document which is a direct descendant of the IFRAME */
.cke_panel_container {
    overflow-y: auto;
    overflow-x: hidden;
}

/*
Here we start the definition of panel lists (e.g. combo panels). The following
is its visual representation:

+-- .cke_panel_block -----------------+
| +-- .cke_panel_grouptitle --------+ |
| |                                 | |
| +---------------------------------+ |
| +-- .cke_panel_list --------------+ |
| | +-- .cke_panel_listItem ------+ | |
| | | +-- a --------------------+ | | |
| | | | +-- span -------------+ | | | |
| | | | |                     | | | | |
| | | | +---------------------+ | | | |
| | | +-------------------------+ | | |
| | +-----------------------------+ | |
| | +-- .cke_panel_listItem ------+ | |
| | | +-- a --------------------+ | | |
| | | | +-- span -------------+ | | | |
| | | | |                     | | | | |
| | | | +---------------------+ | | | |
| | | +-------------------------+ | | |
| | +-----------------------------+ | |
| | ...                             | |
| +---------------------------------+ |
+-------------------------------------+
*/


/* The list of panel items. */
.cke_panel_list {
    list-style-type: none;
    margin: 3px;
    padding: 0;
    white-space: nowrap;
}

/* The item of .cke_panel_list */
.cke_panel_listItem {
    margin: 0;
    padding-bottom: 1px;
}

/* The child of .cke_panel_listItem. These elements contain spans which are
   to display a real name of the property which is visible for an end-user. */
.cke_panel_listItem a {
    padding: 3px 4px;
    display: block;
    border: 1px solid #fff;
    color: inherit !important;
    text-decoration: none;
    overflow: hidden;
    text-overflow: ellipsis;
    border-radius: 2px;
    &:hover, &:focus, &:active {
        background-color: $primary-lighter;
    }
}

/* IE6 */
* html .cke_panel_listItem a {
    width : 100%;

    /* IE is not able to inherit the color, so we must force it to black */
    color: #000;
}

/* IE7 */
*:first-child+html .cke_panel_listItem a {
    /* IE is not able to inherit the color, so we must force it to black */
    color: #000;
}

.cke_panel_listItem.cke_selected a {
    background-color: $primary-light;
    outline: none;
}

.cke_hc .cke_panel_listItem a {
    border-style: none;
}

.cke_hc .cke_panel_listItem a {
    &:hover, &:focus, &:active {
        border: 2px solid;
        padding: 1px 2px;
    }
}

/* The title of the entire panel which is visible on top of the list. */
.cke_panel_grouptitle {
    font-size: 11px;
    font-weight: bold;
    white-space: nowrap;
    margin: 0;
    padding: 6px 6px;

    color: #474747;
    border-bottom: 1px solid $gray;

    background: $gray-lighter;
    &:first-child {
        border-radius: $border-radius $border-radius 0 0;
    }
}

/* The following styles set defaults of the elements used by the Paragraph
   Format panel. */
.cke_panel_listItem {
    p, h1, h2, h3, h4, h5, h6, pre {
        margin-top: 0px;
        margin-bottom: 0px;
    }
}
