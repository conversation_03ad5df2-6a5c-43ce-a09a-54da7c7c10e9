"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sweetalert2";
exports.ids = ["vendor-chunks/sweetalert2"];
exports.modules = {

/***/ "(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js":
/*!**************************************************************!*\
  !*** ./node_modules/sweetalert2/dist/sweetalert2.esm.all.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Swal)\n/* harmony export */ });\n/*!\n* sweetalert2 v11.22.2\n* Released under the MIT License.\n*/\nfunction _assertClassBrand(e, t, n) {\n  if (\"function\" == typeof e ? e === t : e.has(t)) return arguments.length < 3 ? t : n;\n  throw new TypeError(\"Private element is not present on this object\");\n}\nfunction _checkPrivateRedeclaration(e, t) {\n  if (t.has(e)) throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n}\nfunction _classPrivateFieldGet2(s, a) {\n  return s.get(_assertClassBrand(s, a));\n}\nfunction _classPrivateFieldInitSpec(e, t, a) {\n  _checkPrivateRedeclaration(e, t), t.set(e, a);\n}\nfunction _classPrivateFieldSet2(s, a, r) {\n  return s.set(_assertClassBrand(s, a), r), r;\n}\n\nconst RESTORE_FOCUS_TIMEOUT = 100;\n\n/** @type {GlobalState} */\nconst globalState = {};\nconst focusPreviousActiveElement = () => {\n  if (globalState.previousActiveElement instanceof HTMLElement) {\n    globalState.previousActiveElement.focus();\n    globalState.previousActiveElement = null;\n  } else if (document.body) {\n    document.body.focus();\n  }\n};\n\n/**\n * Restore previous active (focused) element\n *\n * @param {boolean} returnFocus\n * @returns {Promise<void>}\n */\nconst restoreActiveElement = returnFocus => {\n  return new Promise(resolve => {\n    if (!returnFocus) {\n      return resolve();\n    }\n    const x = window.scrollX;\n    const y = window.scrollY;\n    globalState.restoreFocusTimeout = setTimeout(() => {\n      focusPreviousActiveElement();\n      resolve();\n    }, RESTORE_FOCUS_TIMEOUT); // issues/900\n\n    window.scrollTo(x, y);\n  });\n};\n\nconst swalPrefix = 'swal2-';\n\n/**\n * @typedef {Record<SwalClass, string>} SwalClasses\n */\n\n/**\n * @typedef {'success' | 'warning' | 'info' | 'question' | 'error'} SwalIcon\n * @typedef {Record<SwalIcon, string>} SwalIcons\n */\n\n/** @type {SwalClass[]} */\nconst classNames = ['container', 'shown', 'height-auto', 'iosfix', 'popup', 'modal', 'no-backdrop', 'no-transition', 'toast', 'toast-shown', 'show', 'hide', 'close', 'title', 'html-container', 'actions', 'confirm', 'deny', 'cancel', 'footer', 'icon', 'icon-content', 'image', 'input', 'file', 'range', 'select', 'radio', 'checkbox', 'label', 'textarea', 'inputerror', 'input-label', 'validation-message', 'progress-steps', 'active-progress-step', 'progress-step', 'progress-step-line', 'loader', 'loading', 'styled', 'top', 'top-start', 'top-end', 'top-left', 'top-right', 'center', 'center-start', 'center-end', 'center-left', 'center-right', 'bottom', 'bottom-start', 'bottom-end', 'bottom-left', 'bottom-right', 'grow-row', 'grow-column', 'grow-fullscreen', 'rtl', 'timer-progress-bar', 'timer-progress-bar-container', 'scrollbar-measure', 'icon-success', 'icon-warning', 'icon-info', 'icon-question', 'icon-error', 'draggable', 'dragging'];\nconst swalClasses = classNames.reduce((acc, className) => {\n  acc[className] = swalPrefix + className;\n  return acc;\n}, /** @type {SwalClasses} */{});\n\n/** @type {SwalIcon[]} */\nconst icons = ['success', 'warning', 'info', 'question', 'error'];\nconst iconTypes = icons.reduce((acc, icon) => {\n  acc[icon] = swalPrefix + icon;\n  return acc;\n}, /** @type {SwalIcons} */{});\n\nconst consolePrefix = 'SweetAlert2:';\n\n/**\n * Capitalize the first letter of a string\n *\n * @param {string} str\n * @returns {string}\n */\nconst capitalizeFirstLetter = str => str.charAt(0).toUpperCase() + str.slice(1);\n\n/**\n * Standardize console warnings\n *\n * @param {string | string[]} message\n */\nconst warn = message => {\n  console.warn(`${consolePrefix} ${typeof message === 'object' ? message.join(' ') : message}`);\n};\n\n/**\n * Standardize console errors\n *\n * @param {string} message\n */\nconst error = message => {\n  console.error(`${consolePrefix} ${message}`);\n};\n\n/**\n * Private global state for `warnOnce`\n *\n * @type {string[]}\n * @private\n */\nconst previousWarnOnceMessages = [];\n\n/**\n * Show a console warning, but only if it hasn't already been shown\n *\n * @param {string} message\n */\nconst warnOnce = message => {\n  if (!previousWarnOnceMessages.includes(message)) {\n    previousWarnOnceMessages.push(message);\n    warn(message);\n  }\n};\n\n/**\n * Show a one-time console warning about deprecated params/methods\n *\n * @param {string} deprecatedParam\n * @param {string?} useInstead\n */\nconst warnAboutDeprecation = (deprecatedParam, useInstead = null) => {\n  warnOnce(`\"${deprecatedParam}\" is deprecated and will be removed in the next major release.${useInstead ? ` Use \"${useInstead}\" instead.` : ''}`);\n};\n\n/**\n * If `arg` is a function, call it (with no arguments or context) and return the result.\n * Otherwise, just pass the value through\n *\n * @param {Function | any} arg\n * @returns {any}\n */\nconst callIfFunction = arg => typeof arg === 'function' ? arg() : arg;\n\n/**\n * @param {any} arg\n * @returns {boolean}\n */\nconst hasToPromiseFn = arg => arg && typeof arg.toPromise === 'function';\n\n/**\n * @param {any} arg\n * @returns {Promise<any>}\n */\nconst asPromise = arg => hasToPromiseFn(arg) ? arg.toPromise() : Promise.resolve(arg);\n\n/**\n * @param {any} arg\n * @returns {boolean}\n */\nconst isPromise = arg => arg && Promise.resolve(arg) === arg;\n\n/**\n * Gets the popup container which contains the backdrop and the popup itself.\n *\n * @returns {HTMLElement | null}\n */\nconst getContainer = () => document.body.querySelector(`.${swalClasses.container}`);\n\n/**\n * @param {string} selectorString\n * @returns {HTMLElement | null}\n */\nconst elementBySelector = selectorString => {\n  const container = getContainer();\n  return container ? container.querySelector(selectorString) : null;\n};\n\n/**\n * @param {string} className\n * @returns {HTMLElement | null}\n */\nconst elementByClass = className => {\n  return elementBySelector(`.${className}`);\n};\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getPopup = () => elementByClass(swalClasses.popup);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getIcon = () => elementByClass(swalClasses.icon);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getIconContent = () => elementByClass(swalClasses['icon-content']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getTitle = () => elementByClass(swalClasses.title);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getHtmlContainer = () => elementByClass(swalClasses['html-container']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getImage = () => elementByClass(swalClasses.image);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getProgressSteps = () => elementByClass(swalClasses['progress-steps']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getValidationMessage = () => elementByClass(swalClasses['validation-message']);\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getConfirmButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.confirm}`));\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getCancelButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.cancel}`));\n\n/**\n * @returns {HTMLButtonElement | null}\n */\nconst getDenyButton = () => (/** @type {HTMLButtonElement} */elementBySelector(`.${swalClasses.actions} .${swalClasses.deny}`));\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getInputLabel = () => elementByClass(swalClasses['input-label']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getLoader = () => elementBySelector(`.${swalClasses.loader}`);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getActions = () => elementByClass(swalClasses.actions);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getFooter = () => elementByClass(swalClasses.footer);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getTimerProgressBar = () => elementByClass(swalClasses['timer-progress-bar']);\n\n/**\n * @returns {HTMLElement | null}\n */\nconst getCloseButton = () => elementByClass(swalClasses.close);\n\n// https://github.com/jkup/focusable/blob/master/index.js\nconst focusable = `\n  a[href],\n  area[href],\n  input:not([disabled]),\n  select:not([disabled]),\n  textarea:not([disabled]),\n  button:not([disabled]),\n  iframe,\n  object,\n  embed,\n  [tabindex=\"0\"],\n  [contenteditable],\n  audio[controls],\n  video[controls],\n  summary\n`;\n/**\n * @returns {HTMLElement[]}\n */\nconst getFocusableElements = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return [];\n  }\n  /** @type {NodeListOf<HTMLElement>} */\n  const focusableElementsWithTabindex = popup.querySelectorAll('[tabindex]:not([tabindex=\"-1\"]):not([tabindex=\"0\"])');\n  const focusableElementsWithTabindexSorted = Array.from(focusableElementsWithTabindex)\n  // sort according to tabindex\n  .sort((a, b) => {\n    const tabindexA = parseInt(a.getAttribute('tabindex') || '0');\n    const tabindexB = parseInt(b.getAttribute('tabindex') || '0');\n    if (tabindexA > tabindexB) {\n      return 1;\n    } else if (tabindexA < tabindexB) {\n      return -1;\n    }\n    return 0;\n  });\n\n  /** @type {NodeListOf<HTMLElement>} */\n  const otherFocusableElements = popup.querySelectorAll(focusable);\n  const otherFocusableElementsFiltered = Array.from(otherFocusableElements).filter(el => el.getAttribute('tabindex') !== '-1');\n  return [...new Set(focusableElementsWithTabindexSorted.concat(otherFocusableElementsFiltered))].filter(el => isVisible$1(el));\n};\n\n/**\n * @returns {boolean}\n */\nconst isModal = () => {\n  return hasClass(document.body, swalClasses.shown) && !hasClass(document.body, swalClasses['toast-shown']) && !hasClass(document.body, swalClasses['no-backdrop']);\n};\n\n/**\n * @returns {boolean}\n */\nconst isToast = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  return hasClass(popup, swalClasses.toast);\n};\n\n/**\n * @returns {boolean}\n */\nconst isLoading = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  return popup.hasAttribute('data-loading');\n};\n\n/**\n * Securely set innerHTML of an element\n * https://github.com/sweetalert2/sweetalert2/issues/1926\n *\n * @param {HTMLElement} elem\n * @param {string} html\n */\nconst setInnerHtml = (elem, html) => {\n  elem.textContent = '';\n  if (html) {\n    const parser = new DOMParser();\n    const parsed = parser.parseFromString(html, `text/html`);\n    const head = parsed.querySelector('head');\n    if (head) {\n      Array.from(head.childNodes).forEach(child => {\n        elem.appendChild(child);\n      });\n    }\n    const body = parsed.querySelector('body');\n    if (body) {\n      Array.from(body.childNodes).forEach(child => {\n        if (child instanceof HTMLVideoElement || child instanceof HTMLAudioElement) {\n          elem.appendChild(child.cloneNode(true)); // https://github.com/sweetalert2/sweetalert2/issues/2507\n        } else {\n          elem.appendChild(child);\n        }\n      });\n    }\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {string} className\n * @returns {boolean}\n */\nconst hasClass = (elem, className) => {\n  if (!className) {\n    return false;\n  }\n  const classList = className.split(/\\s+/);\n  for (let i = 0; i < classList.length; i++) {\n    if (!elem.classList.contains(classList[i])) {\n      return false;\n    }\n  }\n  return true;\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {SweetAlertOptions} params\n */\nconst removeCustomClasses = (elem, params) => {\n  Array.from(elem.classList).forEach(className => {\n    if (!Object.values(swalClasses).includes(className) && !Object.values(iconTypes).includes(className) && !Object.values(params.showClass || {}).includes(className)) {\n      elem.classList.remove(className);\n    }\n  });\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {SweetAlertOptions} params\n * @param {string} className\n */\nconst applyCustomClass = (elem, params, className) => {\n  removeCustomClasses(elem, params);\n  if (!params.customClass) {\n    return;\n  }\n  const customClass = params.customClass[(/** @type {keyof SweetAlertCustomClass} */className)];\n  if (!customClass) {\n    return;\n  }\n  if (typeof customClass !== 'string' && !customClass.forEach) {\n    warn(`Invalid type of customClass.${className}! Expected string or iterable object, got \"${typeof customClass}\"`);\n    return;\n  }\n  addClass(elem, customClass);\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {import('./renderers/renderInput').InputClass | SweetAlertInput} inputClass\n * @returns {HTMLInputElement | null}\n */\nconst getInput$1 = (popup, inputClass) => {\n  if (!inputClass) {\n    return null;\n  }\n  switch (inputClass) {\n    case 'select':\n    case 'textarea':\n    case 'file':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses[inputClass]}`);\n    case 'checkbox':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.checkbox} input`);\n    case 'radio':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:checked`) || popup.querySelector(`.${swalClasses.popup} > .${swalClasses.radio} input:first-child`);\n    case 'range':\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.range} input`);\n    default:\n      return popup.querySelector(`.${swalClasses.popup} > .${swalClasses.input}`);\n  }\n};\n\n/**\n * @param {HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement} input\n */\nconst focusInput = input => {\n  input.focus();\n\n  // place cursor at end of text in text input\n  if (input.type !== 'file') {\n    // http://stackoverflow.com/a/2345915\n    const val = input.value;\n    input.value = '';\n    input.value = val;\n  }\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n * @param {boolean} condition\n */\nconst toggleClass = (target, classList, condition) => {\n  if (!target || !classList) {\n    return;\n  }\n  if (typeof classList === 'string') {\n    classList = classList.split(/\\s+/).filter(Boolean);\n  }\n  classList.forEach(className => {\n    if (Array.isArray(target)) {\n      target.forEach(elem => {\n        if (condition) {\n          elem.classList.add(className);\n        } else {\n          elem.classList.remove(className);\n        }\n      });\n    } else {\n      if (condition) {\n        target.classList.add(className);\n      } else {\n        target.classList.remove(className);\n      }\n    }\n  });\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n */\nconst addClass = (target, classList) => {\n  toggleClass(target, classList, true);\n};\n\n/**\n * @param {HTMLElement | HTMLElement[] | null} target\n * @param {string | string[] | readonly string[] | undefined} classList\n */\nconst removeClass = (target, classList) => {\n  toggleClass(target, classList, false);\n};\n\n/**\n * Get direct child of an element by class name\n *\n * @param {HTMLElement} elem\n * @param {string} className\n * @returns {HTMLElement | undefined}\n */\nconst getDirectChildByClass = (elem, className) => {\n  const children = Array.from(elem.children);\n  for (let i = 0; i < children.length; i++) {\n    const child = children[i];\n    if (child instanceof HTMLElement && hasClass(child, className)) {\n      return child;\n    }\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {string} property\n * @param {*} value\n */\nconst applyNumericalStyle = (elem, property, value) => {\n  if (value === `${parseInt(value)}`) {\n    value = parseInt(value);\n  }\n  if (value || parseInt(value) === 0) {\n    elem.style.setProperty(property, typeof value === 'number' ? `${value}px` : value);\n  } else {\n    elem.style.removeProperty(property);\n  }\n};\n\n/**\n * @param {HTMLElement | null} elem\n * @param {string} display\n */\nconst show = (elem, display = 'flex') => {\n  if (!elem) {\n    return;\n  }\n  elem.style.display = display;\n};\n\n/**\n * @param {HTMLElement | null} elem\n */\nconst hide = elem => {\n  if (!elem) {\n    return;\n  }\n  elem.style.display = 'none';\n};\n\n/**\n * @param {HTMLElement | null} elem\n * @param {string} display\n */\nconst showWhenInnerHtmlPresent = (elem, display = 'block') => {\n  if (!elem) {\n    return;\n  }\n  new MutationObserver(() => {\n    toggle(elem, elem.innerHTML, display);\n  }).observe(elem, {\n    childList: true,\n    subtree: true\n  });\n};\n\n/**\n * @param {HTMLElement} parent\n * @param {string} selector\n * @param {string} property\n * @param {string} value\n */\nconst setStyle = (parent, selector, property, value) => {\n  /** @type {HTMLElement | null} */\n  const el = parent.querySelector(selector);\n  if (el) {\n    el.style.setProperty(property, value);\n  }\n};\n\n/**\n * @param {HTMLElement} elem\n * @param {any} condition\n * @param {string} display\n */\nconst toggle = (elem, condition, display = 'flex') => {\n  if (condition) {\n    show(elem, display);\n  } else {\n    hide(elem);\n  }\n};\n\n/**\n * borrowed from jquery $(elem).is(':visible') implementation\n *\n * @param {HTMLElement | null} elem\n * @returns {boolean}\n */\nconst isVisible$1 = elem => !!(elem && (elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length));\n\n/**\n * @returns {boolean}\n */\nconst allButtonsAreHidden = () => !isVisible$1(getConfirmButton()) && !isVisible$1(getDenyButton()) && !isVisible$1(getCancelButton());\n\n/**\n * @param {HTMLElement} elem\n * @returns {boolean}\n */\nconst isScrollable = elem => !!(elem.scrollHeight > elem.clientHeight);\n\n/**\n * @param {HTMLElement} element\n * @param {HTMLElement} stopElement\n * @returns {boolean}\n */\nconst selfOrParentIsScrollable = (element, stopElement) => {\n  let parent = element;\n  while (parent && parent !== stopElement) {\n    if (isScrollable(parent)) {\n      return true;\n    }\n    parent = parent.parentElement;\n  }\n  return false;\n};\n\n/**\n * borrowed from https://stackoverflow.com/a/46352119\n *\n * @param {HTMLElement} elem\n * @returns {boolean}\n */\nconst hasCssAnimation = elem => {\n  const style = window.getComputedStyle(elem);\n  const animDuration = parseFloat(style.getPropertyValue('animation-duration') || '0');\n  const transDuration = parseFloat(style.getPropertyValue('transition-duration') || '0');\n  return animDuration > 0 || transDuration > 0;\n};\n\n/**\n * @param {number} timer\n * @param {boolean} reset\n */\nconst animateTimerProgressBar = (timer, reset = false) => {\n  const timerProgressBar = getTimerProgressBar();\n  if (!timerProgressBar) {\n    return;\n  }\n  if (isVisible$1(timerProgressBar)) {\n    if (reset) {\n      timerProgressBar.style.transition = 'none';\n      timerProgressBar.style.width = '100%';\n    }\n    setTimeout(() => {\n      timerProgressBar.style.transition = `width ${timer / 1000}s linear`;\n      timerProgressBar.style.width = '0%';\n    }, 10);\n  }\n};\nconst stopTimerProgressBar = () => {\n  const timerProgressBar = getTimerProgressBar();\n  if (!timerProgressBar) {\n    return;\n  }\n  const timerProgressBarWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n  timerProgressBar.style.removeProperty('transition');\n  timerProgressBar.style.width = '100%';\n  const timerProgressBarFullWidth = parseInt(window.getComputedStyle(timerProgressBar).width);\n  const timerProgressBarPercent = timerProgressBarWidth / timerProgressBarFullWidth * 100;\n  timerProgressBar.style.width = `${timerProgressBarPercent}%`;\n};\n\n/**\n * Detect Node env\n *\n * @returns {boolean}\n */\nconst isNodeEnv = () => typeof window === 'undefined' || typeof document === 'undefined';\n\nconst sweetHTML = `\n <div aria-labelledby=\"${swalClasses.title}\" aria-describedby=\"${swalClasses['html-container']}\" class=\"${swalClasses.popup}\" tabindex=\"-1\">\n   <button type=\"button\" class=\"${swalClasses.close}\"></button>\n   <ul class=\"${swalClasses['progress-steps']}\"></ul>\n   <div class=\"${swalClasses.icon}\"></div>\n   <img class=\"${swalClasses.image}\" />\n   <h2 class=\"${swalClasses.title}\" id=\"${swalClasses.title}\"></h2>\n   <div class=\"${swalClasses['html-container']}\" id=\"${swalClasses['html-container']}\"></div>\n   <input class=\"${swalClasses.input}\" id=\"${swalClasses.input}\" />\n   <input type=\"file\" class=\"${swalClasses.file}\" />\n   <div class=\"${swalClasses.range}\">\n     <input type=\"range\" />\n     <output></output>\n   </div>\n   <select class=\"${swalClasses.select}\" id=\"${swalClasses.select}\"></select>\n   <div class=\"${swalClasses.radio}\"></div>\n   <label class=\"${swalClasses.checkbox}\">\n     <input type=\"checkbox\" id=\"${swalClasses.checkbox}\" />\n     <span class=\"${swalClasses.label}\"></span>\n   </label>\n   <textarea class=\"${swalClasses.textarea}\" id=\"${swalClasses.textarea}\"></textarea>\n   <div class=\"${swalClasses['validation-message']}\" id=\"${swalClasses['validation-message']}\"></div>\n   <div class=\"${swalClasses.actions}\">\n     <div class=\"${swalClasses.loader}\"></div>\n     <button type=\"button\" class=\"${swalClasses.confirm}\"></button>\n     <button type=\"button\" class=\"${swalClasses.deny}\"></button>\n     <button type=\"button\" class=\"${swalClasses.cancel}\"></button>\n   </div>\n   <div class=\"${swalClasses.footer}\"></div>\n   <div class=\"${swalClasses['timer-progress-bar-container']}\">\n     <div class=\"${swalClasses['timer-progress-bar']}\"></div>\n   </div>\n </div>\n`.replace(/(^|\\n)\\s*/g, '');\n\n/**\n * @returns {boolean}\n */\nconst resetOldContainer = () => {\n  const oldContainer = getContainer();\n  if (!oldContainer) {\n    return false;\n  }\n  oldContainer.remove();\n  removeClass([document.documentElement, document.body], [swalClasses['no-backdrop'], swalClasses['toast-shown'], swalClasses['has-column']]);\n  return true;\n};\nconst resetValidationMessage$1 = () => {\n  globalState.currentInstance.resetValidationMessage();\n};\nconst addInputChangeListeners = () => {\n  const popup = getPopup();\n  const input = getDirectChildByClass(popup, swalClasses.input);\n  const file = getDirectChildByClass(popup, swalClasses.file);\n  /** @type {HTMLInputElement} */\n  const range = popup.querySelector(`.${swalClasses.range} input`);\n  /** @type {HTMLOutputElement} */\n  const rangeOutput = popup.querySelector(`.${swalClasses.range} output`);\n  const select = getDirectChildByClass(popup, swalClasses.select);\n  /** @type {HTMLInputElement} */\n  const checkbox = popup.querySelector(`.${swalClasses.checkbox} input`);\n  const textarea = getDirectChildByClass(popup, swalClasses.textarea);\n  input.oninput = resetValidationMessage$1;\n  file.onchange = resetValidationMessage$1;\n  select.onchange = resetValidationMessage$1;\n  checkbox.onchange = resetValidationMessage$1;\n  textarea.oninput = resetValidationMessage$1;\n  range.oninput = () => {\n    resetValidationMessage$1();\n    rangeOutput.value = range.value;\n  };\n  range.onchange = () => {\n    resetValidationMessage$1();\n    rangeOutput.value = range.value;\n  };\n};\n\n/**\n * @param {string | HTMLElement} target\n * @returns {HTMLElement}\n */\nconst getTarget = target => typeof target === 'string' ? document.querySelector(target) : target;\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst setupAccessibility = params => {\n  const popup = getPopup();\n  popup.setAttribute('role', params.toast ? 'alert' : 'dialog');\n  popup.setAttribute('aria-live', params.toast ? 'polite' : 'assertive');\n  if (!params.toast) {\n    popup.setAttribute('aria-modal', 'true');\n  }\n};\n\n/**\n * @param {HTMLElement} targetElement\n */\nconst setupRTL = targetElement => {\n  if (window.getComputedStyle(targetElement).direction === 'rtl') {\n    addClass(getContainer(), swalClasses.rtl);\n  }\n};\n\n/**\n * Add modal + backdrop + no-war message for Russians to DOM\n *\n * @param {SweetAlertOptions} params\n */\nconst init = params => {\n  // Clean up the old popup container if it exists\n  const oldContainerExisted = resetOldContainer();\n  if (isNodeEnv()) {\n    error('SweetAlert2 requires document to initialize');\n    return;\n  }\n  const container = document.createElement('div');\n  container.className = swalClasses.container;\n  if (oldContainerExisted) {\n    addClass(container, swalClasses['no-transition']);\n  }\n  setInnerHtml(container, sweetHTML);\n  container.dataset['swal2Theme'] = params.theme;\n  const targetElement = getTarget(params.target);\n  targetElement.appendChild(container);\n  if (params.topLayer) {\n    container.setAttribute('popover', '');\n    container.showPopover();\n  }\n  setupAccessibility(params);\n  setupRTL(targetElement);\n  addInputChangeListeners();\n};\n\n/**\n * @param {HTMLElement | object | string} param\n * @param {HTMLElement} target\n */\nconst parseHtmlToContainer = (param, target) => {\n  // DOM element\n  if (param instanceof HTMLElement) {\n    target.appendChild(param);\n  }\n\n  // Object\n  else if (typeof param === 'object') {\n    handleObject(param, target);\n  }\n\n  // Plain string\n  else if (param) {\n    setInnerHtml(target, param);\n  }\n};\n\n/**\n * @param {any} param\n * @param {HTMLElement} target\n */\nconst handleObject = (param, target) => {\n  // JQuery element(s)\n  if (param.jquery) {\n    handleJqueryElem(target, param);\n  }\n\n  // For other objects use their string representation\n  else {\n    setInnerHtml(target, param.toString());\n  }\n};\n\n/**\n * @param {HTMLElement} target\n * @param {any} elem\n */\nconst handleJqueryElem = (target, elem) => {\n  target.textContent = '';\n  if (0 in elem) {\n    for (let i = 0; i in elem; i++) {\n      target.appendChild(elem[i].cloneNode(true));\n    }\n  } else {\n    target.appendChild(elem.cloneNode(true));\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderActions = (instance, params) => {\n  const actions = getActions();\n  const loader = getLoader();\n  if (!actions || !loader) {\n    return;\n  }\n\n  // Actions (buttons) wrapper\n  if (!params.showConfirmButton && !params.showDenyButton && !params.showCancelButton) {\n    hide(actions);\n  } else {\n    show(actions);\n  }\n\n  // Custom class\n  applyCustomClass(actions, params, 'actions');\n\n  // Render all the buttons\n  renderButtons(actions, loader, params);\n\n  // Loader\n  setInnerHtml(loader, params.loaderHtml || '');\n  applyCustomClass(loader, params, 'loader');\n};\n\n/**\n * @param {HTMLElement} actions\n * @param {HTMLElement} loader\n * @param {SweetAlertOptions} params\n */\nfunction renderButtons(actions, loader, params) {\n  const confirmButton = getConfirmButton();\n  const denyButton = getDenyButton();\n  const cancelButton = getCancelButton();\n  if (!confirmButton || !denyButton || !cancelButton) {\n    return;\n  }\n\n  // Render buttons\n  renderButton(confirmButton, 'confirm', params);\n  renderButton(denyButton, 'deny', params);\n  renderButton(cancelButton, 'cancel', params);\n  handleButtonsStyling(confirmButton, denyButton, cancelButton, params);\n  if (params.reverseButtons) {\n    if (params.toast) {\n      actions.insertBefore(cancelButton, confirmButton);\n      actions.insertBefore(denyButton, confirmButton);\n    } else {\n      actions.insertBefore(cancelButton, loader);\n      actions.insertBefore(denyButton, loader);\n      actions.insertBefore(confirmButton, loader);\n    }\n  }\n}\n\n/**\n * @param {HTMLElement} confirmButton\n * @param {HTMLElement} denyButton\n * @param {HTMLElement} cancelButton\n * @param {SweetAlertOptions} params\n */\nfunction handleButtonsStyling(confirmButton, denyButton, cancelButton, params) {\n  if (!params.buttonsStyling) {\n    removeClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n    return;\n  }\n  addClass([confirmButton, denyButton, cancelButton], swalClasses.styled);\n\n  // Apply custom background colors to action buttons\n  if (params.confirmButtonColor) {\n    confirmButton.style.setProperty('--swal2-confirm-button-background-color', params.confirmButtonColor);\n  }\n  if (params.denyButtonColor) {\n    denyButton.style.setProperty('--swal2-deny-button-background-color', params.denyButtonColor);\n  }\n  if (params.cancelButtonColor) {\n    cancelButton.style.setProperty('--swal2-cancel-button-background-color', params.cancelButtonColor);\n  }\n\n  // Apply the outline color to action buttons\n  applyOutlineColor(confirmButton);\n  applyOutlineColor(denyButton);\n  applyOutlineColor(cancelButton);\n}\n\n/**\n * @param {HTMLElement} button\n */\nfunction applyOutlineColor(button) {\n  const buttonStyle = window.getComputedStyle(button);\n  if (buttonStyle.getPropertyValue('--swal2-action-button-focus-box-shadow')) {\n    // If the button already has a custom outline color, no need to change it\n    return;\n  }\n  const outlineColor = buttonStyle.backgroundColor.replace(/rgba?\\((\\d+), (\\d+), (\\d+).*/, 'rgba($1, $2, $3, 0.5)');\n  button.style.setProperty('--swal2-action-button-focus-box-shadow', buttonStyle.getPropertyValue('--swal2-outline').replace(/ rgba\\(.*/, ` ${outlineColor}`));\n}\n\n/**\n * @param {HTMLElement} button\n * @param {'confirm' | 'deny' | 'cancel'} buttonType\n * @param {SweetAlertOptions} params\n */\nfunction renderButton(button, buttonType, params) {\n  const buttonName = /** @type {'Confirm' | 'Deny' | 'Cancel'} */capitalizeFirstLetter(buttonType);\n  toggle(button, params[`show${buttonName}Button`], 'inline-block');\n  setInnerHtml(button, params[`${buttonType}ButtonText`] || ''); // Set caption text\n  button.setAttribute('aria-label', params[`${buttonType}ButtonAriaLabel`] || ''); // ARIA label\n\n  // Add buttons custom classes\n  button.className = swalClasses[buttonType];\n  applyCustomClass(button, params, `${buttonType}Button`);\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderCloseButton = (instance, params) => {\n  const closeButton = getCloseButton();\n  if (!closeButton) {\n    return;\n  }\n  setInnerHtml(closeButton, params.closeButtonHtml || '');\n\n  // Custom class\n  applyCustomClass(closeButton, params, 'closeButton');\n  toggle(closeButton, params.showCloseButton);\n  closeButton.setAttribute('aria-label', params.closeButtonAriaLabel || '');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderContainer = (instance, params) => {\n  const container = getContainer();\n  if (!container) {\n    return;\n  }\n  handleBackdropParam(container, params.backdrop);\n  handlePositionParam(container, params.position);\n  handleGrowParam(container, params.grow);\n\n  // Custom class\n  applyCustomClass(container, params, 'container');\n};\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['backdrop']} backdrop\n */\nfunction handleBackdropParam(container, backdrop) {\n  if (typeof backdrop === 'string') {\n    container.style.background = backdrop;\n  } else if (!backdrop) {\n    addClass([document.documentElement, document.body], swalClasses['no-backdrop']);\n  }\n}\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['position']} position\n */\nfunction handlePositionParam(container, position) {\n  if (!position) {\n    return;\n  }\n  if (position in swalClasses) {\n    addClass(container, swalClasses[position]);\n  } else {\n    warn('The \"position\" parameter is not valid, defaulting to \"center\"');\n    addClass(container, swalClasses.center);\n  }\n}\n\n/**\n * @param {HTMLElement} container\n * @param {SweetAlertOptions['grow']} grow\n */\nfunction handleGrowParam(container, grow) {\n  if (!grow) {\n    return;\n  }\n  addClass(container, swalClasses[`grow-${grow}`]);\n}\n\n/**\n * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n * This is the approach that Babel will probably take to implement private methods/fields\n *   https://github.com/tc39/proposal-private-methods\n *   https://github.com/babel/babel/pull/7555\n * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n *   then we can use that language feature.\n */\n\nvar privateProps = {\n  innerParams: new WeakMap(),\n  domCache: new WeakMap()\n};\n\n/// <reference path=\"../../../../sweetalert2.d.ts\"/>\n\n\n/** @type {InputClass[]} */\nconst inputClasses = ['input', 'file', 'range', 'select', 'radio', 'checkbox', 'textarea'];\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderInput = (instance, params) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const innerParams = privateProps.innerParams.get(instance);\n  const rerender = !innerParams || params.input !== innerParams.input;\n  inputClasses.forEach(inputClass => {\n    const inputContainer = getDirectChildByClass(popup, swalClasses[inputClass]);\n    if (!inputContainer) {\n      return;\n    }\n\n    // set attributes\n    setAttributes(inputClass, params.inputAttributes);\n\n    // set class\n    inputContainer.className = swalClasses[inputClass];\n    if (rerender) {\n      hide(inputContainer);\n    }\n  });\n  if (params.input) {\n    if (rerender) {\n      showInput(params);\n    }\n    // set custom class\n    setCustomClass(params);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst showInput = params => {\n  if (!params.input) {\n    return;\n  }\n  if (!renderInputType[params.input]) {\n    error(`Unexpected type of input! Expected ${Object.keys(renderInputType).join(' | ')}, got \"${params.input}\"`);\n    return;\n  }\n  const inputContainer = getInputContainer(params.input);\n  if (!inputContainer) {\n    return;\n  }\n  const input = renderInputType[params.input](inputContainer, params);\n  show(inputContainer);\n\n  // input autofocus\n  if (params.inputAutoFocus) {\n    setTimeout(() => {\n      focusInput(input);\n    });\n  }\n};\n\n/**\n * @param {HTMLInputElement} input\n */\nconst removeAttributes = input => {\n  for (let i = 0; i < input.attributes.length; i++) {\n    const attrName = input.attributes[i].name;\n    if (!['id', 'type', 'value', 'style'].includes(attrName)) {\n      input.removeAttribute(attrName);\n    }\n  }\n};\n\n/**\n * @param {InputClass} inputClass\n * @param {SweetAlertOptions['inputAttributes']} inputAttributes\n */\nconst setAttributes = (inputClass, inputAttributes) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const input = getInput$1(popup, inputClass);\n  if (!input) {\n    return;\n  }\n  removeAttributes(input);\n  for (const attr in inputAttributes) {\n    input.setAttribute(attr, inputAttributes[attr]);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nconst setCustomClass = params => {\n  if (!params.input) {\n    return;\n  }\n  const inputContainer = getInputContainer(params.input);\n  if (inputContainer) {\n    applyCustomClass(inputContainer, params, 'input');\n  }\n};\n\n/**\n * @param {HTMLInputElement | HTMLTextAreaElement} input\n * @param {SweetAlertOptions} params\n */\nconst setInputPlaceholder = (input, params) => {\n  if (!input.placeholder && params.inputPlaceholder) {\n    input.placeholder = params.inputPlaceholder;\n  }\n};\n\n/**\n * @param {Input} input\n * @param {Input} prependTo\n * @param {SweetAlertOptions} params\n */\nconst setInputLabel = (input, prependTo, params) => {\n  if (params.inputLabel) {\n    const label = document.createElement('label');\n    const labelClass = swalClasses['input-label'];\n    label.setAttribute('for', input.id);\n    label.className = labelClass;\n    if (typeof params.customClass === 'object') {\n      addClass(label, params.customClass.inputLabel);\n    }\n    label.innerText = params.inputLabel;\n    prependTo.insertAdjacentElement('beforebegin', label);\n  }\n};\n\n/**\n * @param {SweetAlertInput} inputType\n * @returns {HTMLElement | undefined}\n */\nconst getInputContainer = inputType => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  return getDirectChildByClass(popup, swalClasses[(/** @type {SwalClass} */inputType)] || swalClasses.input);\n};\n\n/**\n * @param {HTMLInputElement | HTMLOutputElement | HTMLTextAreaElement} input\n * @param {SweetAlertOptions['inputValue']} inputValue\n */\nconst checkAndSetInputValue = (input, inputValue) => {\n  if (['string', 'number'].includes(typeof inputValue)) {\n    input.value = `${inputValue}`;\n  } else if (!isPromise(inputValue)) {\n    warn(`Unexpected type of inputValue! Expected \"string\", \"number\" or \"Promise\", got \"${typeof inputValue}\"`);\n  }\n};\n\n/** @type {Record<SweetAlertInput, (input: Input | HTMLElement, params: SweetAlertOptions) => Input>} */\nconst renderInputType = {};\n\n/**\n * @param {HTMLInputElement} input\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.text = renderInputType.email = renderInputType.password = renderInputType.number = renderInputType.tel = renderInputType.url = renderInputType.search = renderInputType.date = renderInputType['datetime-local'] = renderInputType.time = renderInputType.week = renderInputType.month = /** @type {(input: Input | HTMLElement, params: SweetAlertOptions) => Input} */\n(input, params) => {\n  checkAndSetInputValue(input, params.inputValue);\n  setInputLabel(input, input, params);\n  setInputPlaceholder(input, params);\n  input.type = params.input;\n  return input;\n};\n\n/**\n * @param {HTMLInputElement} input\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.file = (input, params) => {\n  setInputLabel(input, input, params);\n  setInputPlaceholder(input, params);\n  return input;\n};\n\n/**\n * @param {HTMLInputElement} range\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.range = (range, params) => {\n  const rangeInput = range.querySelector('input');\n  const rangeOutput = range.querySelector('output');\n  checkAndSetInputValue(rangeInput, params.inputValue);\n  rangeInput.type = params.input;\n  checkAndSetInputValue(rangeOutput, params.inputValue);\n  setInputLabel(rangeInput, range, params);\n  return range;\n};\n\n/**\n * @param {HTMLSelectElement} select\n * @param {SweetAlertOptions} params\n * @returns {HTMLSelectElement}\n */\nrenderInputType.select = (select, params) => {\n  select.textContent = '';\n  if (params.inputPlaceholder) {\n    const placeholder = document.createElement('option');\n    setInnerHtml(placeholder, params.inputPlaceholder);\n    placeholder.value = '';\n    placeholder.disabled = true;\n    placeholder.selected = true;\n    select.appendChild(placeholder);\n  }\n  setInputLabel(select, select, params);\n  return select;\n};\n\n/**\n * @param {HTMLInputElement} radio\n * @returns {HTMLInputElement}\n */\nrenderInputType.radio = radio => {\n  radio.textContent = '';\n  return radio;\n};\n\n/**\n * @param {HTMLLabelElement} checkboxContainer\n * @param {SweetAlertOptions} params\n * @returns {HTMLInputElement}\n */\nrenderInputType.checkbox = (checkboxContainer, params) => {\n  const checkbox = getInput$1(getPopup(), 'checkbox');\n  checkbox.value = '1';\n  checkbox.checked = Boolean(params.inputValue);\n  const label = checkboxContainer.querySelector('span');\n  setInnerHtml(label, params.inputPlaceholder || params.inputLabel);\n  return checkbox;\n};\n\n/**\n * @param {HTMLTextAreaElement} textarea\n * @param {SweetAlertOptions} params\n * @returns {HTMLTextAreaElement}\n */\nrenderInputType.textarea = (textarea, params) => {\n  checkAndSetInputValue(textarea, params.inputValue);\n  setInputPlaceholder(textarea, params);\n  setInputLabel(textarea, textarea, params);\n\n  /**\n   * @param {HTMLElement} el\n   * @returns {number}\n   */\n  const getMargin = el => parseInt(window.getComputedStyle(el).marginLeft) + parseInt(window.getComputedStyle(el).marginRight);\n\n  // https://github.com/sweetalert2/sweetalert2/issues/2291\n  setTimeout(() => {\n    // https://github.com/sweetalert2/sweetalert2/issues/1699\n    if ('MutationObserver' in window) {\n      const initialPopupWidth = parseInt(window.getComputedStyle(getPopup()).width);\n      const textareaResizeHandler = () => {\n        // check if texarea is still in document (i.e. popup wasn't closed in the meantime)\n        if (!document.body.contains(textarea)) {\n          return;\n        }\n        const textareaWidth = textarea.offsetWidth + getMargin(textarea);\n        if (textareaWidth > initialPopupWidth) {\n          getPopup().style.width = `${textareaWidth}px`;\n        } else {\n          applyNumericalStyle(getPopup(), 'width', params.width);\n        }\n      };\n      new MutationObserver(textareaResizeHandler).observe(textarea, {\n        attributes: true,\n        attributeFilter: ['style']\n      });\n    }\n  });\n  return textarea;\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderContent = (instance, params) => {\n  const htmlContainer = getHtmlContainer();\n  if (!htmlContainer) {\n    return;\n  }\n  showWhenInnerHtmlPresent(htmlContainer);\n  applyCustomClass(htmlContainer, params, 'htmlContainer');\n\n  // Content as HTML\n  if (params.html) {\n    parseHtmlToContainer(params.html, htmlContainer);\n    show(htmlContainer, 'block');\n  }\n\n  // Content as plain text\n  else if (params.text) {\n    htmlContainer.textContent = params.text;\n    show(htmlContainer, 'block');\n  }\n\n  // No content\n  else {\n    hide(htmlContainer);\n  }\n  renderInput(instance, params);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderFooter = (instance, params) => {\n  const footer = getFooter();\n  if (!footer) {\n    return;\n  }\n  showWhenInnerHtmlPresent(footer);\n  toggle(footer, params.footer, 'block');\n  if (params.footer) {\n    parseHtmlToContainer(params.footer, footer);\n  }\n\n  // Custom class\n  applyCustomClass(footer, params, 'footer');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderIcon = (instance, params) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  const icon = getIcon();\n  if (!icon) {\n    return;\n  }\n\n  // if the given icon already rendered, apply the styling without re-rendering the icon\n  if (innerParams && params.icon === innerParams.icon) {\n    // Custom or default content\n    setContent(icon, params);\n    applyStyles(icon, params);\n    return;\n  }\n  if (!params.icon && !params.iconHtml) {\n    hide(icon);\n    return;\n  }\n  if (params.icon && Object.keys(iconTypes).indexOf(params.icon) === -1) {\n    error(`Unknown icon! Expected \"success\", \"error\", \"warning\", \"info\" or \"question\", got \"${params.icon}\"`);\n    hide(icon);\n    return;\n  }\n  show(icon);\n\n  // Custom or default content\n  setContent(icon, params);\n  applyStyles(icon, params);\n\n  // Animate icon\n  addClass(icon, params.showClass && params.showClass.icon);\n\n  // Re-adjust the success icon on system theme change\n  const colorSchemeQueryList = window.matchMedia('(prefers-color-scheme: dark)');\n  colorSchemeQueryList.addEventListener('change', adjustSuccessIconBackgroundColor);\n};\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst applyStyles = (icon, params) => {\n  for (const [iconType, iconClassName] of Object.entries(iconTypes)) {\n    if (params.icon !== iconType) {\n      removeClass(icon, iconClassName);\n    }\n  }\n  addClass(icon, params.icon && iconTypes[params.icon]);\n\n  // Icon color\n  setColor(icon, params);\n\n  // Success icon background color\n  adjustSuccessIconBackgroundColor();\n\n  // Custom class\n  applyCustomClass(icon, params, 'icon');\n};\n\n// Adjust success icon background color to match the popup background color\nconst adjustSuccessIconBackgroundColor = () => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const popupBackgroundColor = window.getComputedStyle(popup).getPropertyValue('background-color');\n  /** @type {NodeListOf<HTMLElement>} */\n  const successIconParts = popup.querySelectorAll('[class^=swal2-success-circular-line], .swal2-success-fix');\n  for (let i = 0; i < successIconParts.length; i++) {\n    successIconParts[i].style.backgroundColor = popupBackgroundColor;\n  }\n};\n\n/**\n *\n * @param {SweetAlertOptions} params\n * @returns {string}\n */\nconst successIconHtml = params => `\n  ${params.animation ? '<div class=\"swal2-success-circular-line-left\"></div>' : ''}\n  <span class=\"swal2-success-line-tip\"></span> <span class=\"swal2-success-line-long\"></span>\n  <div class=\"swal2-success-ring\"></div>\n  ${params.animation ? '<div class=\"swal2-success-fix\"></div>' : ''}\n  ${params.animation ? '<div class=\"swal2-success-circular-line-right\"></div>' : ''}\n`;\nconst errorIconHtml = `\n  <span class=\"swal2-x-mark\">\n    <span class=\"swal2-x-mark-line-left\"></span>\n    <span class=\"swal2-x-mark-line-right\"></span>\n  </span>\n`;\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst setContent = (icon, params) => {\n  if (!params.icon && !params.iconHtml) {\n    return;\n  }\n  let oldContent = icon.innerHTML;\n  let newContent = '';\n  if (params.iconHtml) {\n    newContent = iconContent(params.iconHtml);\n  } else if (params.icon === 'success') {\n    newContent = successIconHtml(params);\n    oldContent = oldContent.replace(/ style=\".*?\"/g, ''); // undo adjustSuccessIconBackgroundColor()\n  } else if (params.icon === 'error') {\n    newContent = errorIconHtml;\n  } else if (params.icon) {\n    const defaultIconHtml = {\n      question: '?',\n      warning: '!',\n      info: 'i'\n    };\n    newContent = iconContent(defaultIconHtml[params.icon]);\n  }\n  if (oldContent.trim() !== newContent.trim()) {\n    setInnerHtml(icon, newContent);\n  }\n};\n\n/**\n * @param {HTMLElement} icon\n * @param {SweetAlertOptions} params\n */\nconst setColor = (icon, params) => {\n  if (!params.iconColor) {\n    return;\n  }\n  icon.style.color = params.iconColor;\n  icon.style.borderColor = params.iconColor;\n  for (const sel of ['.swal2-success-line-tip', '.swal2-success-line-long', '.swal2-x-mark-line-left', '.swal2-x-mark-line-right']) {\n    setStyle(icon, sel, 'background-color', params.iconColor);\n  }\n  setStyle(icon, '.swal2-success-ring', 'border-color', params.iconColor);\n};\n\n/**\n * @param {string} content\n * @returns {string}\n */\nconst iconContent = content => `<div class=\"${swalClasses['icon-content']}\">${content}</div>`;\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderImage = (instance, params) => {\n  const image = getImage();\n  if (!image) {\n    return;\n  }\n  if (!params.imageUrl) {\n    hide(image);\n    return;\n  }\n  show(image, '');\n\n  // Src, alt\n  image.setAttribute('src', params.imageUrl);\n  image.setAttribute('alt', params.imageAlt || '');\n\n  // Width, height\n  applyNumericalStyle(image, 'width', params.imageWidth);\n  applyNumericalStyle(image, 'height', params.imageHeight);\n\n  // Class\n  image.className = swalClasses.image;\n  applyCustomClass(image, params, 'image');\n};\n\nlet dragging = false;\nlet mousedownX = 0;\nlet mousedownY = 0;\nlet initialX = 0;\nlet initialY = 0;\n\n/**\n * @param {HTMLElement} popup\n */\nconst addDraggableListeners = popup => {\n  popup.addEventListener('mousedown', down);\n  document.body.addEventListener('mousemove', move);\n  popup.addEventListener('mouseup', up);\n  popup.addEventListener('touchstart', down);\n  document.body.addEventListener('touchmove', move);\n  popup.addEventListener('touchend', up);\n};\n\n/**\n * @param {HTMLElement} popup\n */\nconst removeDraggableListeners = popup => {\n  popup.removeEventListener('mousedown', down);\n  document.body.removeEventListener('mousemove', move);\n  popup.removeEventListener('mouseup', up);\n  popup.removeEventListener('touchstart', down);\n  document.body.removeEventListener('touchmove', move);\n  popup.removeEventListener('touchend', up);\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n */\nconst down = event => {\n  const popup = getPopup();\n  if (event.target === popup || getIcon().contains(/** @type {HTMLElement} */event.target)) {\n    dragging = true;\n    const clientXY = getClientXY(event);\n    mousedownX = clientXY.clientX;\n    mousedownY = clientXY.clientY;\n    initialX = parseInt(popup.style.insetInlineStart) || 0;\n    initialY = parseInt(popup.style.insetBlockStart) || 0;\n    addClass(popup, 'swal2-dragging');\n  }\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n */\nconst move = event => {\n  const popup = getPopup();\n  if (dragging) {\n    let {\n      clientX,\n      clientY\n    } = getClientXY(event);\n    popup.style.insetInlineStart = `${initialX + (clientX - mousedownX)}px`;\n    popup.style.insetBlockStart = `${initialY + (clientY - mousedownY)}px`;\n  }\n};\nconst up = () => {\n  const popup = getPopup();\n  dragging = false;\n  removeClass(popup, 'swal2-dragging');\n};\n\n/**\n * @param {MouseEvent | TouchEvent} event\n * @returns {{ clientX: number, clientY: number }}\n */\nconst getClientXY = event => {\n  let clientX = 0,\n    clientY = 0;\n  if (event.type.startsWith('mouse')) {\n    clientX = /** @type {MouseEvent} */event.clientX;\n    clientY = /** @type {MouseEvent} */event.clientY;\n  } else if (event.type.startsWith('touch')) {\n    clientX = /** @type {TouchEvent} */event.touches[0].clientX;\n    clientY = /** @type {TouchEvent} */event.touches[0].clientY;\n  }\n  return {\n    clientX,\n    clientY\n  };\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderPopup = (instance, params) => {\n  const container = getContainer();\n  const popup = getPopup();\n  if (!container || !popup) {\n    return;\n  }\n\n  // Width\n  // https://github.com/sweetalert2/sweetalert2/issues/2170\n  if (params.toast) {\n    applyNumericalStyle(container, 'width', params.width);\n    popup.style.width = '100%';\n    const loader = getLoader();\n    if (loader) {\n      popup.insertBefore(loader, getIcon());\n    }\n  } else {\n    applyNumericalStyle(popup, 'width', params.width);\n  }\n\n  // Padding\n  applyNumericalStyle(popup, 'padding', params.padding);\n\n  // Color\n  if (params.color) {\n    popup.style.color = params.color;\n  }\n\n  // Background\n  if (params.background) {\n    popup.style.background = params.background;\n  }\n  hide(getValidationMessage());\n\n  // Classes\n  addClasses$1(popup, params);\n  if (params.draggable && !params.toast) {\n    addClass(popup, swalClasses.draggable);\n    addDraggableListeners(popup);\n  } else {\n    removeClass(popup, swalClasses.draggable);\n    removeDraggableListeners(popup);\n  }\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} params\n */\nconst addClasses$1 = (popup, params) => {\n  const showClass = params.showClass || {};\n  // Default Class + showClass when updating Swal.update({})\n  popup.className = `${swalClasses.popup} ${isVisible$1(popup) ? showClass.popup : ''}`;\n  if (params.toast) {\n    addClass([document.documentElement, document.body], swalClasses['toast-shown']);\n    addClass(popup, swalClasses.toast);\n  } else {\n    addClass(popup, swalClasses.modal);\n  }\n\n  // Custom class\n  applyCustomClass(popup, params, 'popup');\n  // TODO: remove in the next major\n  if (typeof params.customClass === 'string') {\n    addClass(popup, params.customClass);\n  }\n\n  // Icon class (#1842)\n  if (params.icon) {\n    addClass(popup, swalClasses[`icon-${params.icon}`]);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderProgressSteps = (instance, params) => {\n  const progressStepsContainer = getProgressSteps();\n  if (!progressStepsContainer) {\n    return;\n  }\n  const {\n    progressSteps,\n    currentProgressStep\n  } = params;\n  if (!progressSteps || progressSteps.length === 0 || currentProgressStep === undefined) {\n    hide(progressStepsContainer);\n    return;\n  }\n  show(progressStepsContainer);\n  progressStepsContainer.textContent = '';\n  if (currentProgressStep >= progressSteps.length) {\n    warn('Invalid currentProgressStep parameter, it should be less than progressSteps.length ' + '(currentProgressStep like JS arrays starts from 0)');\n  }\n  progressSteps.forEach((step, index) => {\n    const stepEl = createStepElement(step);\n    progressStepsContainer.appendChild(stepEl);\n    if (index === currentProgressStep) {\n      addClass(stepEl, swalClasses['active-progress-step']);\n    }\n    if (index !== progressSteps.length - 1) {\n      const lineEl = createLineElement(params);\n      progressStepsContainer.appendChild(lineEl);\n    }\n  });\n};\n\n/**\n * @param {string} step\n * @returns {HTMLLIElement}\n */\nconst createStepElement = step => {\n  const stepEl = document.createElement('li');\n  addClass(stepEl, swalClasses['progress-step']);\n  setInnerHtml(stepEl, step);\n  return stepEl;\n};\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {HTMLLIElement}\n */\nconst createLineElement = params => {\n  const lineEl = document.createElement('li');\n  addClass(lineEl, swalClasses['progress-step-line']);\n  if (params.progressStepsDistance) {\n    applyNumericalStyle(lineEl, 'width', params.progressStepsDistance);\n  }\n  return lineEl;\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst renderTitle = (instance, params) => {\n  const title = getTitle();\n  if (!title) {\n    return;\n  }\n  showWhenInnerHtmlPresent(title);\n  toggle(title, params.title || params.titleText, 'block');\n  if (params.title) {\n    parseHtmlToContainer(params.title, title);\n  }\n  if (params.titleText) {\n    title.innerText = params.titleText;\n  }\n\n  // Custom class\n  applyCustomClass(title, params, 'title');\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst render = (instance, params) => {\n  renderPopup(instance, params);\n  renderContainer(instance, params);\n  renderProgressSteps(instance, params);\n  renderIcon(instance, params);\n  renderImage(instance, params);\n  renderTitle(instance, params);\n  renderCloseButton(instance, params);\n  renderContent(instance, params);\n  renderActions(instance, params);\n  renderFooter(instance, params);\n  const popup = getPopup();\n  if (typeof params.didRender === 'function' && popup) {\n    params.didRender(popup);\n  }\n  globalState.eventEmitter.emit('didRender', popup);\n};\n\n/*\n * Global function to determine if SweetAlert2 popup is shown\n */\nconst isVisible = () => {\n  return isVisible$1(getPopup());\n};\n\n/*\n * Global function to click 'Confirm' button\n */\nconst clickConfirm = () => {\n  var _dom$getConfirmButton;\n  return (_dom$getConfirmButton = getConfirmButton()) === null || _dom$getConfirmButton === void 0 ? void 0 : _dom$getConfirmButton.click();\n};\n\n/*\n * Global function to click 'Deny' button\n */\nconst clickDeny = () => {\n  var _dom$getDenyButton;\n  return (_dom$getDenyButton = getDenyButton()) === null || _dom$getDenyButton === void 0 ? void 0 : _dom$getDenyButton.click();\n};\n\n/*\n * Global function to click 'Cancel' button\n */\nconst clickCancel = () => {\n  var _dom$getCancelButton;\n  return (_dom$getCancelButton = getCancelButton()) === null || _dom$getCancelButton === void 0 ? void 0 : _dom$getCancelButton.click();\n};\n\n/** @typedef {'cancel' | 'backdrop' | 'close' | 'esc' | 'timer'} DismissReason */\n\n/** @type {Record<DismissReason, DismissReason>} */\nconst DismissReason = Object.freeze({\n  cancel: 'cancel',\n  backdrop: 'backdrop',\n  close: 'close',\n  esc: 'esc',\n  timer: 'timer'\n});\n\n/**\n * @param {GlobalState} globalState\n */\nconst removeKeydownHandler = globalState => {\n  if (globalState.keydownTarget && globalState.keydownHandlerAdded) {\n    globalState.keydownTarget.removeEventListener('keydown', globalState.keydownHandler, {\n      capture: globalState.keydownListenerCapture\n    });\n    globalState.keydownHandlerAdded = false;\n  }\n};\n\n/**\n * @param {GlobalState} globalState\n * @param {SweetAlertOptions} innerParams\n * @param {*} dismissWith\n */\nconst addKeydownHandler = (globalState, innerParams, dismissWith) => {\n  removeKeydownHandler(globalState);\n  if (!innerParams.toast) {\n    globalState.keydownHandler = e => keydownHandler(innerParams, e, dismissWith);\n    globalState.keydownTarget = innerParams.keydownListenerCapture ? window : getPopup();\n    globalState.keydownListenerCapture = innerParams.keydownListenerCapture;\n    globalState.keydownTarget.addEventListener('keydown', globalState.keydownHandler, {\n      capture: globalState.keydownListenerCapture\n    });\n    globalState.keydownHandlerAdded = true;\n  }\n};\n\n/**\n * @param {number} index\n * @param {number} increment\n */\nconst setFocus = (index, increment) => {\n  var _dom$getPopup;\n  const focusableElements = getFocusableElements();\n  // search for visible elements and select the next possible match\n  if (focusableElements.length) {\n    index = index + increment;\n\n    // shift + tab when .swal2-popup is focused\n    if (index === -2) {\n      index = focusableElements.length - 1;\n    }\n\n    // rollover to first item\n    if (index === focusableElements.length) {\n      index = 0;\n\n      // go to last item\n    } else if (index === -1) {\n      index = focusableElements.length - 1;\n    }\n    focusableElements[index].focus();\n    return;\n  }\n  // no visible focusable elements, focus the popup\n  (_dom$getPopup = getPopup()) === null || _dom$getPopup === void 0 || _dom$getPopup.focus();\n};\nconst arrowKeysNextButton = ['ArrowRight', 'ArrowDown'];\nconst arrowKeysPreviousButton = ['ArrowLeft', 'ArrowUp'];\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {KeyboardEvent} event\n * @param {Function} dismissWith\n */\nconst keydownHandler = (innerParams, event, dismissWith) => {\n  if (!innerParams) {\n    return; // This instance has already been destroyed\n  }\n\n  // Ignore keydown during IME composition\n  // https://developer.mozilla.org/en-US/docs/Web/API/Document/keydown_event#ignoring_keydown_during_ime_composition\n  // https://github.com/sweetalert2/sweetalert2/issues/720\n  // https://github.com/sweetalert2/sweetalert2/issues/2406\n  if (event.isComposing || event.keyCode === 229) {\n    return;\n  }\n  if (innerParams.stopKeydownPropagation) {\n    event.stopPropagation();\n  }\n\n  // ENTER\n  if (event.key === 'Enter') {\n    handleEnter(event, innerParams);\n  }\n\n  // TAB\n  else if (event.key === 'Tab') {\n    handleTab(event);\n  }\n\n  // ARROWS - switch focus between buttons\n  else if ([...arrowKeysNextButton, ...arrowKeysPreviousButton].includes(event.key)) {\n    handleArrows(event.key);\n  }\n\n  // ESC\n  else if (event.key === 'Escape') {\n    handleEsc(event, innerParams, dismissWith);\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n * @param {SweetAlertOptions} innerParams\n */\nconst handleEnter = (event, innerParams) => {\n  // https://github.com/sweetalert2/sweetalert2/issues/2386\n  if (!callIfFunction(innerParams.allowEnterKey)) {\n    return;\n  }\n  const input = getInput$1(getPopup(), innerParams.input);\n  if (event.target && input && event.target instanceof HTMLElement && event.target.outerHTML === input.outerHTML) {\n    if (['textarea', 'file'].includes(innerParams.input)) {\n      return; // do not submit\n    }\n    clickConfirm();\n    event.preventDefault();\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n */\nconst handleTab = event => {\n  const targetElement = event.target;\n  const focusableElements = getFocusableElements();\n  let btnIndex = -1;\n  for (let i = 0; i < focusableElements.length; i++) {\n    if (targetElement === focusableElements[i]) {\n      btnIndex = i;\n      break;\n    }\n  }\n\n  // Cycle to the next button\n  if (!event.shiftKey) {\n    setFocus(btnIndex, 1);\n  }\n\n  // Cycle to the prev button\n  else {\n    setFocus(btnIndex, -1);\n  }\n  event.stopPropagation();\n  event.preventDefault();\n};\n\n/**\n * @param {string} key\n */\nconst handleArrows = key => {\n  const actions = getActions();\n  const confirmButton = getConfirmButton();\n  const denyButton = getDenyButton();\n  const cancelButton = getCancelButton();\n  if (!actions || !confirmButton || !denyButton || !cancelButton) {\n    return;\n  }\n  /** @type HTMLElement[] */\n  const buttons = [confirmButton, denyButton, cancelButton];\n  if (document.activeElement instanceof HTMLElement && !buttons.includes(document.activeElement)) {\n    return;\n  }\n  const sibling = arrowKeysNextButton.includes(key) ? 'nextElementSibling' : 'previousElementSibling';\n  let buttonToFocus = document.activeElement;\n  if (!buttonToFocus) {\n    return;\n  }\n  for (let i = 0; i < actions.children.length; i++) {\n    buttonToFocus = buttonToFocus[sibling];\n    if (!buttonToFocus) {\n      return;\n    }\n    if (buttonToFocus instanceof HTMLButtonElement && isVisible$1(buttonToFocus)) {\n      break;\n    }\n  }\n  if (buttonToFocus instanceof HTMLButtonElement) {\n    buttonToFocus.focus();\n  }\n};\n\n/**\n * @param {KeyboardEvent} event\n * @param {SweetAlertOptions} innerParams\n * @param {Function} dismissWith\n */\nconst handleEsc = (event, innerParams, dismissWith) => {\n  event.preventDefault();\n  if (callIfFunction(innerParams.allowEscapeKey)) {\n    dismissWith(DismissReason.esc);\n  }\n};\n\n/**\n * This module contains `WeakMap`s for each effectively-\"private  property\" that a `Swal` has.\n * For example, to set the private property \"foo\" of `this` to \"bar\", you can `privateProps.foo.set(this, 'bar')`\n * This is the approach that Babel will probably take to implement private methods/fields\n *   https://github.com/tc39/proposal-private-methods\n *   https://github.com/babel/babel/pull/7555\n * Once we have the changes from that PR in Babel, and our core class fits reasonable in *one module*\n *   then we can use that language feature.\n */\n\nvar privateMethods = {\n  swalPromiseResolve: new WeakMap(),\n  swalPromiseReject: new WeakMap()\n};\n\n// From https://developer.paciellogroup.com/blog/2018/06/the-current-state-of-modal-dialog-accessibility/\n// Adding aria-hidden=\"true\" to elements outside of the active modal dialog ensures that\n// elements not within the active modal dialog will not be surfaced if a user opens a screen\n// reader’s list of elements (headings, form controls, landmarks, etc.) in the document.\n\nconst setAriaHidden = () => {\n  const container = getContainer();\n  const bodyChildren = Array.from(document.body.children);\n  bodyChildren.forEach(el => {\n    if (el.contains(container)) {\n      return;\n    }\n    if (el.hasAttribute('aria-hidden')) {\n      el.setAttribute('data-previous-aria-hidden', el.getAttribute('aria-hidden') || '');\n    }\n    el.setAttribute('aria-hidden', 'true');\n  });\n};\nconst unsetAriaHidden = () => {\n  const bodyChildren = Array.from(document.body.children);\n  bodyChildren.forEach(el => {\n    if (el.hasAttribute('data-previous-aria-hidden')) {\n      el.setAttribute('aria-hidden', el.getAttribute('data-previous-aria-hidden') || '');\n      el.removeAttribute('data-previous-aria-hidden');\n    } else {\n      el.removeAttribute('aria-hidden');\n    }\n  });\n};\n\n// @ts-ignore\nconst isSafariOrIOS = typeof window !== 'undefined' && !!window.GestureEvent; // true for Safari desktop + all iOS browsers https://stackoverflow.com/a/70585394\n\n/**\n * Fix iOS scrolling\n * http://stackoverflow.com/q/39626302\n */\nconst iOSfix = () => {\n  if (isSafariOrIOS && !hasClass(document.body, swalClasses.iosfix)) {\n    const offset = document.body.scrollTop;\n    document.body.style.top = `${offset * -1}px`;\n    addClass(document.body, swalClasses.iosfix);\n    lockBodyScroll();\n  }\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1246\n */\nconst lockBodyScroll = () => {\n  const container = getContainer();\n  if (!container) {\n    return;\n  }\n  /** @type {boolean} */\n  let preventTouchMove;\n  /**\n   * @param {TouchEvent} event\n   */\n  container.ontouchstart = event => {\n    preventTouchMove = shouldPreventTouchMove(event);\n  };\n  /**\n   * @param {TouchEvent} event\n   */\n  container.ontouchmove = event => {\n    if (preventTouchMove) {\n      event.preventDefault();\n      event.stopPropagation();\n    }\n  };\n};\n\n/**\n * @param {TouchEvent} event\n * @returns {boolean}\n */\nconst shouldPreventTouchMove = event => {\n  const target = event.target;\n  const container = getContainer();\n  const htmlContainer = getHtmlContainer();\n  if (!container || !htmlContainer) {\n    return false;\n  }\n  if (isStylus(event) || isZoom(event)) {\n    return false;\n  }\n  if (target === container) {\n    return true;\n  }\n  if (!isScrollable(container) && target instanceof HTMLElement && !selfOrParentIsScrollable(target, htmlContainer) &&\n  // #2823\n  target.tagName !== 'INPUT' &&\n  // #1603\n  target.tagName !== 'TEXTAREA' &&\n  // #2266\n  !(isScrollable(htmlContainer) &&\n  // #1944\n  htmlContainer.contains(target))) {\n    return true;\n  }\n  return false;\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1786\n *\n * @param {*} event\n * @returns {boolean}\n */\nconst isStylus = event => {\n  return event.touches && event.touches.length && event.touches[0].touchType === 'stylus';\n};\n\n/**\n * https://github.com/sweetalert2/sweetalert2/issues/1891\n *\n * @param {TouchEvent} event\n * @returns {boolean}\n */\nconst isZoom = event => {\n  return event.touches && event.touches.length > 1;\n};\nconst undoIOSfix = () => {\n  if (hasClass(document.body, swalClasses.iosfix)) {\n    const offset = parseInt(document.body.style.top, 10);\n    removeClass(document.body, swalClasses.iosfix);\n    document.body.style.top = '';\n    document.body.scrollTop = offset * -1;\n  }\n};\n\n/**\n * Measure scrollbar width for padding body during modal show/hide\n * https://github.com/twbs/bootstrap/blob/master/js/src/modal.js\n *\n * @returns {number}\n */\nconst measureScrollbar = () => {\n  const scrollDiv = document.createElement('div');\n  scrollDiv.className = swalClasses['scrollbar-measure'];\n  document.body.appendChild(scrollDiv);\n  const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth;\n  document.body.removeChild(scrollDiv);\n  return scrollbarWidth;\n};\n\n/**\n * Remember state in cases where opening and handling a modal will fiddle with it.\n * @type {number | null}\n */\nlet previousBodyPadding = null;\n\n/**\n * @param {string} initialBodyOverflow\n */\nconst replaceScrollbarWithPadding = initialBodyOverflow => {\n  // for queues, do not do this more than once\n  if (previousBodyPadding !== null) {\n    return;\n  }\n  // if the body has overflow\n  if (document.body.scrollHeight > window.innerHeight || initialBodyOverflow === 'scroll' // https://github.com/sweetalert2/sweetalert2/issues/2663\n  ) {\n    // add padding so the content doesn't shift after removal of scrollbar\n    previousBodyPadding = parseInt(window.getComputedStyle(document.body).getPropertyValue('padding-right'));\n    document.body.style.paddingRight = `${previousBodyPadding + measureScrollbar()}px`;\n  }\n};\nconst undoReplaceScrollbarWithPadding = () => {\n  if (previousBodyPadding !== null) {\n    document.body.style.paddingRight = `${previousBodyPadding}px`;\n    previousBodyPadding = null;\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} container\n * @param {boolean} returnFocus\n * @param {Function} didClose\n */\nfunction removePopupAndResetState(instance, container, returnFocus, didClose) {\n  if (isToast()) {\n    triggerDidCloseAndDispose(instance, didClose);\n  } else {\n    restoreActiveElement(returnFocus).then(() => triggerDidCloseAndDispose(instance, didClose));\n    removeKeydownHandler(globalState);\n  }\n\n  // workaround for https://github.com/sweetalert2/sweetalert2/issues/2088\n  // for some reason removing the container in Safari will scroll the document to bottom\n  if (isSafariOrIOS) {\n    container.setAttribute('style', 'display:none !important');\n    container.removeAttribute('class');\n    container.innerHTML = '';\n  } else {\n    container.remove();\n  }\n  if (isModal()) {\n    undoReplaceScrollbarWithPadding();\n    undoIOSfix();\n    unsetAriaHidden();\n  }\n  removeBodyClasses();\n}\n\n/**\n * Remove SweetAlert2 classes from body\n */\nfunction removeBodyClasses() {\n  removeClass([document.documentElement, document.body], [swalClasses.shown, swalClasses['height-auto'], swalClasses['no-backdrop'], swalClasses['toast-shown']]);\n}\n\n/**\n * Instance method to close sweetAlert\n *\n * @param {any} resolveValue\n */\nfunction close(resolveValue) {\n  resolveValue = prepareResolveValue(resolveValue);\n  const swalPromiseResolve = privateMethods.swalPromiseResolve.get(this);\n  const didClose = triggerClosePopup(this);\n  if (this.isAwaitingPromise) {\n    // A swal awaiting for a promise (after a click on Confirm or Deny) cannot be dismissed anymore #2335\n    if (!resolveValue.isDismissed) {\n      handleAwaitingPromise(this);\n      swalPromiseResolve(resolveValue);\n    }\n  } else if (didClose) {\n    // Resolve Swal promise\n    swalPromiseResolve(resolveValue);\n  }\n}\nconst triggerClosePopup = instance => {\n  const popup = getPopup();\n  if (!popup) {\n    return false;\n  }\n  const innerParams = privateProps.innerParams.get(instance);\n  if (!innerParams || hasClass(popup, innerParams.hideClass.popup)) {\n    return false;\n  }\n  removeClass(popup, innerParams.showClass.popup);\n  addClass(popup, innerParams.hideClass.popup);\n  const backdrop = getContainer();\n  removeClass(backdrop, innerParams.showClass.backdrop);\n  addClass(backdrop, innerParams.hideClass.backdrop);\n  handlePopupAnimation(instance, popup, innerParams);\n  return true;\n};\n\n/**\n * @param {any} error\n */\nfunction rejectPromise(error) {\n  const rejectPromise = privateMethods.swalPromiseReject.get(this);\n  handleAwaitingPromise(this);\n  if (rejectPromise) {\n    // Reject Swal promise\n    rejectPromise(error);\n  }\n}\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleAwaitingPromise = instance => {\n  if (instance.isAwaitingPromise) {\n    delete instance.isAwaitingPromise;\n    // The instance might have been previously partly destroyed, we must resume the destroy process in this case #2335\n    if (!privateProps.innerParams.get(instance)) {\n      instance._destroy();\n    }\n  }\n};\n\n/**\n * @param {any} resolveValue\n * @returns {SweetAlertResult}\n */\nconst prepareResolveValue = resolveValue => {\n  // When user calls Swal.close()\n  if (typeof resolveValue === 'undefined') {\n    return {\n      isConfirmed: false,\n      isDenied: false,\n      isDismissed: true\n    };\n  }\n  return Object.assign({\n    isConfirmed: false,\n    isDenied: false,\n    isDismissed: false\n  }, resolveValue);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} innerParams\n */\nconst handlePopupAnimation = (instance, popup, innerParams) => {\n  var _globalState$eventEmi;\n  const container = getContainer();\n  // If animation is supported, animate\n  const animationIsSupported = hasCssAnimation(popup);\n  if (typeof innerParams.willClose === 'function') {\n    innerParams.willClose(popup);\n  }\n  (_globalState$eventEmi = globalState.eventEmitter) === null || _globalState$eventEmi === void 0 || _globalState$eventEmi.emit('willClose', popup);\n  if (animationIsSupported) {\n    animatePopup(instance, popup, container, innerParams.returnFocus, innerParams.didClose);\n  } else {\n    // Otherwise, remove immediately\n    removePopupAndResetState(instance, container, innerParams.returnFocus, innerParams.didClose);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {HTMLElement} popup\n * @param {HTMLElement} container\n * @param {boolean} returnFocus\n * @param {Function} didClose\n */\nconst animatePopup = (instance, popup, container, returnFocus, didClose) => {\n  globalState.swalCloseEventFinishedCallback = removePopupAndResetState.bind(null, instance, container, returnFocus, didClose);\n  /**\n   * @param {AnimationEvent | TransitionEvent} e\n   */\n  const swalCloseAnimationFinished = function (e) {\n    if (e.target === popup) {\n      var _globalState$swalClos;\n      (_globalState$swalClos = globalState.swalCloseEventFinishedCallback) === null || _globalState$swalClos === void 0 || _globalState$swalClos.call(globalState);\n      delete globalState.swalCloseEventFinishedCallback;\n      popup.removeEventListener('animationend', swalCloseAnimationFinished);\n      popup.removeEventListener('transitionend', swalCloseAnimationFinished);\n    }\n  };\n  popup.addEventListener('animationend', swalCloseAnimationFinished);\n  popup.addEventListener('transitionend', swalCloseAnimationFinished);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {Function} didClose\n */\nconst triggerDidCloseAndDispose = (instance, didClose) => {\n  setTimeout(() => {\n    var _globalState$eventEmi2;\n    if (typeof didClose === 'function') {\n      didClose.bind(instance.params)();\n    }\n    (_globalState$eventEmi2 = globalState.eventEmitter) === null || _globalState$eventEmi2 === void 0 || _globalState$eventEmi2.emit('didClose');\n    // instance might have been destroyed already\n    if (instance._destroy) {\n      instance._destroy();\n    }\n  });\n};\n\n/**\n * Shows loader (spinner), this is useful with AJAX requests.\n * By default the loader be shown instead of the \"Confirm\" button.\n *\n * @param {HTMLButtonElement | null} [buttonToReplace]\n */\nconst showLoading = buttonToReplace => {\n  let popup = getPopup();\n  if (!popup) {\n    new Swal();\n  }\n  popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  const loader = getLoader();\n  if (isToast()) {\n    hide(getIcon());\n  } else {\n    replaceButton(popup, buttonToReplace);\n  }\n  show(loader);\n  popup.setAttribute('data-loading', 'true');\n  popup.setAttribute('aria-busy', 'true');\n  popup.focus();\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {HTMLButtonElement | null} [buttonToReplace]\n */\nconst replaceButton = (popup, buttonToReplace) => {\n  const actions = getActions();\n  const loader = getLoader();\n  if (!actions || !loader) {\n    return;\n  }\n  if (!buttonToReplace && isVisible$1(getConfirmButton())) {\n    buttonToReplace = getConfirmButton();\n  }\n  show(actions);\n  if (buttonToReplace) {\n    hide(buttonToReplace);\n    loader.setAttribute('data-button-to-replace', buttonToReplace.className);\n    actions.insertBefore(loader, buttonToReplace);\n  }\n  addClass([popup, actions], swalClasses.loading);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputOptionsAndValue = (instance, params) => {\n  if (params.input === 'select' || params.input === 'radio') {\n    handleInputOptions(instance, params);\n  } else if (['text', 'email', 'number', 'tel', 'textarea'].some(i => i === params.input) && (hasToPromiseFn(params.inputValue) || isPromise(params.inputValue))) {\n    showLoading(getConfirmButton());\n    handleInputValue(instance, params);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} innerParams\n * @returns {SweetAlertInputValue}\n */\nconst getInputValue = (instance, innerParams) => {\n  const input = instance.getInput();\n  if (!input) {\n    return null;\n  }\n  switch (innerParams.input) {\n    case 'checkbox':\n      return getCheckboxValue(input);\n    case 'radio':\n      return getRadioValue(input);\n    case 'file':\n      return getFileValue(input);\n    default:\n      return innerParams.inputAutoTrim ? input.value.trim() : input.value;\n  }\n};\n\n/**\n * @param {HTMLInputElement} input\n * @returns {number}\n */\nconst getCheckboxValue = input => input.checked ? 1 : 0;\n\n/**\n * @param {HTMLInputElement} input\n * @returns {string | null}\n */\nconst getRadioValue = input => input.checked ? input.value : null;\n\n/**\n * @param {HTMLInputElement} input\n * @returns {FileList | File | null}\n */\nconst getFileValue = input => input.files && input.files.length ? input.getAttribute('multiple') !== null ? input.files : input.files[0] : null;\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputOptions = (instance, params) => {\n  const popup = getPopup();\n  if (!popup) {\n    return;\n  }\n  /**\n   * @param {Record<string, any>} inputOptions\n   */\n  const processInputOptions = inputOptions => {\n    if (params.input === 'select') {\n      populateSelectOptions(popup, formatInputOptions(inputOptions), params);\n    } else if (params.input === 'radio') {\n      populateRadioOptions(popup, formatInputOptions(inputOptions), params);\n    }\n  };\n  if (hasToPromiseFn(params.inputOptions) || isPromise(params.inputOptions)) {\n    showLoading(getConfirmButton());\n    asPromise(params.inputOptions).then(inputOptions => {\n      instance.hideLoading();\n      processInputOptions(inputOptions);\n    });\n  } else if (typeof params.inputOptions === 'object') {\n    processInputOptions(params.inputOptions);\n  } else {\n    error(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof params.inputOptions}`);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertOptions} params\n */\nconst handleInputValue = (instance, params) => {\n  const input = instance.getInput();\n  if (!input) {\n    return;\n  }\n  hide(input);\n  asPromise(params.inputValue).then(inputValue => {\n    input.value = params.input === 'number' ? `${parseFloat(inputValue) || 0}` : `${inputValue}`;\n    show(input);\n    input.focus();\n    instance.hideLoading();\n  }).catch(err => {\n    error(`Error in inputValue promise: ${err}`);\n    input.value = '';\n    show(input);\n    input.focus();\n    instance.hideLoading();\n  });\n};\n\n/**\n * @param {HTMLElement} popup\n * @param {InputOptionFlattened[]} inputOptions\n * @param {SweetAlertOptions} params\n */\nfunction populateSelectOptions(popup, inputOptions, params) {\n  const select = getDirectChildByClass(popup, swalClasses.select);\n  if (!select) {\n    return;\n  }\n  /**\n   * @param {HTMLElement} parent\n   * @param {string} optionLabel\n   * @param {string} optionValue\n   */\n  const renderOption = (parent, optionLabel, optionValue) => {\n    const option = document.createElement('option');\n    option.value = optionValue;\n    setInnerHtml(option, optionLabel);\n    option.selected = isSelected(optionValue, params.inputValue);\n    parent.appendChild(option);\n  };\n  inputOptions.forEach(inputOption => {\n    const optionValue = inputOption[0];\n    const optionLabel = inputOption[1];\n    // <optgroup> spec:\n    // https://www.w3.org/TR/html401/interact/forms.html#h-17.6\n    // \"...all OPTGROUP elements must be specified directly within a SELECT element (i.e., groups may not be nested)...\"\n    // check whether this is a <optgroup>\n    if (Array.isArray(optionLabel)) {\n      // if it is an array, then it is an <optgroup>\n      const optgroup = document.createElement('optgroup');\n      optgroup.label = optionValue;\n      optgroup.disabled = false; // not configurable for now\n      select.appendChild(optgroup);\n      optionLabel.forEach(o => renderOption(optgroup, o[1], o[0]));\n    } else {\n      // case of <option>\n      renderOption(select, optionLabel, optionValue);\n    }\n  });\n  select.focus();\n}\n\n/**\n * @param {HTMLElement} popup\n * @param {InputOptionFlattened[]} inputOptions\n * @param {SweetAlertOptions} params\n */\nfunction populateRadioOptions(popup, inputOptions, params) {\n  const radio = getDirectChildByClass(popup, swalClasses.radio);\n  if (!radio) {\n    return;\n  }\n  inputOptions.forEach(inputOption => {\n    const radioValue = inputOption[0];\n    const radioLabel = inputOption[1];\n    const radioInput = document.createElement('input');\n    const radioLabelElement = document.createElement('label');\n    radioInput.type = 'radio';\n    radioInput.name = swalClasses.radio;\n    radioInput.value = radioValue;\n    if (isSelected(radioValue, params.inputValue)) {\n      radioInput.checked = true;\n    }\n    const label = document.createElement('span');\n    setInnerHtml(label, radioLabel);\n    label.className = swalClasses.label;\n    radioLabelElement.appendChild(radioInput);\n    radioLabelElement.appendChild(label);\n    radio.appendChild(radioLabelElement);\n  });\n  const radios = radio.querySelectorAll('input');\n  if (radios.length) {\n    radios[0].focus();\n  }\n}\n\n/**\n * Converts `inputOptions` into an array of `[value, label]`s\n *\n * @param {Record<string, any>} inputOptions\n * @typedef {string[]} InputOptionFlattened\n * @returns {InputOptionFlattened[]}\n */\nconst formatInputOptions = inputOptions => {\n  /** @type {InputOptionFlattened[]} */\n  const result = [];\n  if (inputOptions instanceof Map) {\n    inputOptions.forEach((value, key) => {\n      let valueFormatted = value;\n      if (typeof valueFormatted === 'object') {\n        // case of <optgroup>\n        valueFormatted = formatInputOptions(valueFormatted);\n      }\n      result.push([key, valueFormatted]);\n    });\n  } else {\n    Object.keys(inputOptions).forEach(key => {\n      let valueFormatted = inputOptions[key];\n      if (typeof valueFormatted === 'object') {\n        // case of <optgroup>\n        valueFormatted = formatInputOptions(valueFormatted);\n      }\n      result.push([key, valueFormatted]);\n    });\n  }\n  return result;\n};\n\n/**\n * @param {string} optionValue\n * @param {SweetAlertInputValue} inputValue\n * @returns {boolean}\n */\nconst isSelected = (optionValue, inputValue) => {\n  return !!inputValue && inputValue.toString() === optionValue.toString();\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleConfirmButtonClick = instance => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableButtons();\n  if (innerParams.input) {\n    handleConfirmOrDenyWithInput(instance, 'confirm');\n  } else {\n    confirm(instance, true);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst handleDenyButtonClick = instance => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableButtons();\n  if (innerParams.returnInputValueOnDeny) {\n    handleConfirmOrDenyWithInput(instance, 'deny');\n  } else {\n    deny(instance, false);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {Function} dismissWith\n */\nconst handleCancelButtonClick = (instance, dismissWith) => {\n  instance.disableButtons();\n  dismissWith(DismissReason.cancel);\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {'confirm' | 'deny'} type\n */\nconst handleConfirmOrDenyWithInput = (instance, type) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  if (!innerParams.input) {\n    error(`The \"input\" parameter is needed to be set when using returnInputValueOn${capitalizeFirstLetter(type)}`);\n    return;\n  }\n  const input = instance.getInput();\n  const inputValue = getInputValue(instance, innerParams);\n  if (innerParams.inputValidator) {\n    handleInputValidator(instance, inputValue, type);\n  } else if (input && !input.checkValidity()) {\n    instance.enableButtons();\n    instance.showValidationMessage(innerParams.validationMessage || input.validationMessage);\n  } else if (type === 'deny') {\n    deny(instance, inputValue);\n  } else {\n    confirm(instance, inputValue);\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {SweetAlertInputValue} inputValue\n * @param {'confirm' | 'deny'} type\n */\nconst handleInputValidator = (instance, inputValue, type) => {\n  const innerParams = privateProps.innerParams.get(instance);\n  instance.disableInput();\n  const validationPromise = Promise.resolve().then(() => asPromise(innerParams.inputValidator(inputValue, innerParams.validationMessage)));\n  validationPromise.then(validationMessage => {\n    instance.enableButtons();\n    instance.enableInput();\n    if (validationMessage) {\n      instance.showValidationMessage(validationMessage);\n    } else if (type === 'deny') {\n      deny(instance, inputValue);\n    } else {\n      confirm(instance, inputValue);\n    }\n  });\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst deny = (instance, value) => {\n  const innerParams = privateProps.innerParams.get(instance || undefined);\n  if (innerParams.showLoaderOnDeny) {\n    showLoading(getDenyButton());\n  }\n  if (innerParams.preDeny) {\n    instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preDeny's promise is received\n    const preDenyPromise = Promise.resolve().then(() => asPromise(innerParams.preDeny(value, innerParams.validationMessage)));\n    preDenyPromise.then(preDenyValue => {\n      if (preDenyValue === false) {\n        instance.hideLoading();\n        handleAwaitingPromise(instance);\n      } else {\n        instance.close({\n          isDenied: true,\n          value: typeof preDenyValue === 'undefined' ? value : preDenyValue\n        });\n      }\n    }).catch(error => rejectWith(instance || undefined, error));\n  } else {\n    instance.close({\n      isDenied: true,\n      value\n    });\n  }\n};\n\n/**\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst succeedWith = (instance, value) => {\n  instance.close({\n    isConfirmed: true,\n    value\n  });\n};\n\n/**\n *\n * @param {SweetAlert} instance\n * @param {string} error\n */\nconst rejectWith = (instance, error) => {\n  instance.rejectPromise(error);\n};\n\n/**\n *\n * @param {SweetAlert} instance\n * @param {any} value\n */\nconst confirm = (instance, value) => {\n  const innerParams = privateProps.innerParams.get(instance || undefined);\n  if (innerParams.showLoaderOnConfirm) {\n    showLoading();\n  }\n  if (innerParams.preConfirm) {\n    instance.resetValidationMessage();\n    instance.isAwaitingPromise = true; // Flagging the instance as awaiting a promise so it's own promise's reject/resolve methods doesn't get destroyed until the result from this preConfirm's promise is received\n    const preConfirmPromise = Promise.resolve().then(() => asPromise(innerParams.preConfirm(value, innerParams.validationMessage)));\n    preConfirmPromise.then(preConfirmValue => {\n      if (isVisible$1(getValidationMessage()) || preConfirmValue === false) {\n        instance.hideLoading();\n        handleAwaitingPromise(instance);\n      } else {\n        succeedWith(instance, typeof preConfirmValue === 'undefined' ? value : preConfirmValue);\n      }\n    }).catch(error => rejectWith(instance || undefined, error));\n  } else {\n    succeedWith(instance, value);\n  }\n};\n\n/**\n * Hides loader and shows back the button which was hidden by .showLoading()\n */\nfunction hideLoading() {\n  // do nothing if popup is closed\n  const innerParams = privateProps.innerParams.get(this);\n  if (!innerParams) {\n    return;\n  }\n  const domCache = privateProps.domCache.get(this);\n  hide(domCache.loader);\n  if (isToast()) {\n    if (innerParams.icon) {\n      show(getIcon());\n    }\n  } else {\n    showRelatedButton(domCache);\n  }\n  removeClass([domCache.popup, domCache.actions], swalClasses.loading);\n  domCache.popup.removeAttribute('aria-busy');\n  domCache.popup.removeAttribute('data-loading');\n  domCache.confirmButton.disabled = false;\n  domCache.denyButton.disabled = false;\n  domCache.cancelButton.disabled = false;\n}\nconst showRelatedButton = domCache => {\n  const buttonToReplace = domCache.popup.getElementsByClassName(domCache.loader.getAttribute('data-button-to-replace'));\n  if (buttonToReplace.length) {\n    show(buttonToReplace[0], 'inline-block');\n  } else if (allButtonsAreHidden()) {\n    hide(domCache.actions);\n  }\n};\n\n/**\n * Gets the input DOM node, this method works with input parameter.\n *\n * @returns {HTMLInputElement | null}\n */\nfunction getInput() {\n  const innerParams = privateProps.innerParams.get(this);\n  const domCache = privateProps.domCache.get(this);\n  if (!domCache) {\n    return null;\n  }\n  return getInput$1(domCache.popup, innerParams.input);\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {string[]} buttons\n * @param {boolean} disabled\n */\nfunction setButtonsDisabled(instance, buttons, disabled) {\n  const domCache = privateProps.domCache.get(instance);\n  buttons.forEach(button => {\n    domCache[button].disabled = disabled;\n  });\n}\n\n/**\n * @param {HTMLInputElement | null} input\n * @param {boolean} disabled\n */\nfunction setInputDisabled(input, disabled) {\n  const popup = getPopup();\n  if (!popup || !input) {\n    return;\n  }\n  if (input.type === 'radio') {\n    /** @type {NodeListOf<HTMLInputElement>} */\n    const radios = popup.querySelectorAll(`[name=\"${swalClasses.radio}\"]`);\n    for (let i = 0; i < radios.length; i++) {\n      radios[i].disabled = disabled;\n    }\n  } else {\n    input.disabled = disabled;\n  }\n}\n\n/**\n * Enable all the buttons\n * @this {SweetAlert}\n */\nfunction enableButtons() {\n  setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], false);\n}\n\n/**\n * Disable all the buttons\n * @this {SweetAlert}\n */\nfunction disableButtons() {\n  setButtonsDisabled(this, ['confirmButton', 'denyButton', 'cancelButton'], true);\n}\n\n/**\n * Enable the input field\n * @this {SweetAlert}\n */\nfunction enableInput() {\n  setInputDisabled(this.getInput(), false);\n}\n\n/**\n * Disable the input field\n * @this {SweetAlert}\n */\nfunction disableInput() {\n  setInputDisabled(this.getInput(), true);\n}\n\n/**\n * Show block with validation message\n *\n * @param {string} error\n * @this {SweetAlert}\n */\nfunction showValidationMessage(error) {\n  const domCache = privateProps.domCache.get(this);\n  const params = privateProps.innerParams.get(this);\n  setInnerHtml(domCache.validationMessage, error);\n  domCache.validationMessage.className = swalClasses['validation-message'];\n  if (params.customClass && params.customClass.validationMessage) {\n    addClass(domCache.validationMessage, params.customClass.validationMessage);\n  }\n  show(domCache.validationMessage);\n  const input = this.getInput();\n  if (input) {\n    input.setAttribute('aria-invalid', 'true');\n    input.setAttribute('aria-describedby', swalClasses['validation-message']);\n    focusInput(input);\n    addClass(input, swalClasses.inputerror);\n  }\n}\n\n/**\n * Hide block with validation message\n *\n * @this {SweetAlert}\n */\nfunction resetValidationMessage() {\n  const domCache = privateProps.domCache.get(this);\n  if (domCache.validationMessage) {\n    hide(domCache.validationMessage);\n  }\n  const input = this.getInput();\n  if (input) {\n    input.removeAttribute('aria-invalid');\n    input.removeAttribute('aria-describedby');\n    removeClass(input, swalClasses.inputerror);\n  }\n}\n\nconst defaultParams = {\n  title: '',\n  titleText: '',\n  text: '',\n  html: '',\n  footer: '',\n  icon: undefined,\n  iconColor: undefined,\n  iconHtml: undefined,\n  template: undefined,\n  toast: false,\n  draggable: false,\n  animation: true,\n  theme: 'light',\n  showClass: {\n    popup: 'swal2-show',\n    backdrop: 'swal2-backdrop-show',\n    icon: 'swal2-icon-show'\n  },\n  hideClass: {\n    popup: 'swal2-hide',\n    backdrop: 'swal2-backdrop-hide',\n    icon: 'swal2-icon-hide'\n  },\n  customClass: {},\n  target: 'body',\n  color: undefined,\n  backdrop: true,\n  heightAuto: true,\n  allowOutsideClick: true,\n  allowEscapeKey: true,\n  allowEnterKey: true,\n  stopKeydownPropagation: true,\n  keydownListenerCapture: false,\n  showConfirmButton: true,\n  showDenyButton: false,\n  showCancelButton: false,\n  preConfirm: undefined,\n  preDeny: undefined,\n  confirmButtonText: 'OK',\n  confirmButtonAriaLabel: '',\n  confirmButtonColor: undefined,\n  denyButtonText: 'No',\n  denyButtonAriaLabel: '',\n  denyButtonColor: undefined,\n  cancelButtonText: 'Cancel',\n  cancelButtonAriaLabel: '',\n  cancelButtonColor: undefined,\n  buttonsStyling: true,\n  reverseButtons: false,\n  focusConfirm: true,\n  focusDeny: false,\n  focusCancel: false,\n  returnFocus: true,\n  showCloseButton: false,\n  closeButtonHtml: '&times;',\n  closeButtonAriaLabel: 'Close this dialog',\n  loaderHtml: '',\n  showLoaderOnConfirm: false,\n  showLoaderOnDeny: false,\n  imageUrl: undefined,\n  imageWidth: undefined,\n  imageHeight: undefined,\n  imageAlt: '',\n  timer: undefined,\n  timerProgressBar: false,\n  width: undefined,\n  padding: undefined,\n  background: undefined,\n  input: undefined,\n  inputPlaceholder: '',\n  inputLabel: '',\n  inputValue: '',\n  inputOptions: {},\n  inputAutoFocus: true,\n  inputAutoTrim: true,\n  inputAttributes: {},\n  inputValidator: undefined,\n  returnInputValueOnDeny: false,\n  validationMessage: undefined,\n  grow: false,\n  position: 'center',\n  progressSteps: [],\n  currentProgressStep: undefined,\n  progressStepsDistance: undefined,\n  willOpen: undefined,\n  didOpen: undefined,\n  didRender: undefined,\n  willClose: undefined,\n  didClose: undefined,\n  didDestroy: undefined,\n  scrollbarPadding: true,\n  topLayer: false\n};\nconst updatableParams = ['allowEscapeKey', 'allowOutsideClick', 'background', 'buttonsStyling', 'cancelButtonAriaLabel', 'cancelButtonColor', 'cancelButtonText', 'closeButtonAriaLabel', 'closeButtonHtml', 'color', 'confirmButtonAriaLabel', 'confirmButtonColor', 'confirmButtonText', 'currentProgressStep', 'customClass', 'denyButtonAriaLabel', 'denyButtonColor', 'denyButtonText', 'didClose', 'didDestroy', 'draggable', 'footer', 'hideClass', 'html', 'icon', 'iconColor', 'iconHtml', 'imageAlt', 'imageHeight', 'imageUrl', 'imageWidth', 'preConfirm', 'preDeny', 'progressSteps', 'returnFocus', 'reverseButtons', 'showCancelButton', 'showCloseButton', 'showConfirmButton', 'showDenyButton', 'text', 'title', 'titleText', 'theme', 'willClose'];\n\n/** @type {Record<string, string | undefined>} */\nconst deprecatedParams = {\n  allowEnterKey: undefined\n};\nconst toastIncompatibleParams = ['allowOutsideClick', 'allowEnterKey', 'backdrop', 'draggable', 'focusConfirm', 'focusDeny', 'focusCancel', 'returnFocus', 'heightAuto', 'keydownListenerCapture'];\n\n/**\n * Is valid parameter\n *\n * @param {string} paramName\n * @returns {boolean}\n */\nconst isValidParameter = paramName => {\n  return Object.prototype.hasOwnProperty.call(defaultParams, paramName);\n};\n\n/**\n * Is valid parameter for Swal.update() method\n *\n * @param {string} paramName\n * @returns {boolean}\n */\nconst isUpdatableParameter = paramName => {\n  return updatableParams.indexOf(paramName) !== -1;\n};\n\n/**\n * Is deprecated parameter\n *\n * @param {string} paramName\n * @returns {string | undefined}\n */\nconst isDeprecatedParameter = paramName => {\n  return deprecatedParams[paramName];\n};\n\n/**\n * @param {string} param\n */\nconst checkIfParamIsValid = param => {\n  if (!isValidParameter(param)) {\n    warn(`Unknown parameter \"${param}\"`);\n  }\n};\n\n/**\n * @param {string} param\n */\nconst checkIfToastParamIsValid = param => {\n  if (toastIncompatibleParams.includes(param)) {\n    warn(`The parameter \"${param}\" is incompatible with toasts`);\n  }\n};\n\n/**\n * @param {string} param\n */\nconst checkIfParamIsDeprecated = param => {\n  const isDeprecated = isDeprecatedParameter(param);\n  if (isDeprecated) {\n    warnAboutDeprecation(param, isDeprecated);\n  }\n};\n\n/**\n * Show relevant warnings for given params\n *\n * @param {SweetAlertOptions} params\n */\nconst showWarningsForParams = params => {\n  if (params.backdrop === false && params.allowOutsideClick) {\n    warn('\"allowOutsideClick\" parameter requires `backdrop` parameter to be set to `true`');\n  }\n  if (params.theme && !['light', 'dark', 'auto', 'minimal', 'borderless', 'embed-iframe', 'bulma', 'bulma-light', 'bulma-dark'].includes(params.theme)) {\n    warn(`Invalid theme \"${params.theme}\"`);\n  }\n  for (const param in params) {\n    checkIfParamIsValid(param);\n    if (params.toast) {\n      checkIfToastParamIsValid(param);\n    }\n    checkIfParamIsDeprecated(param);\n  }\n};\n\n/**\n * Updates popup parameters.\n *\n * @param {SweetAlertOptions} params\n */\nfunction update(params) {\n  const container = getContainer();\n  const popup = getPopup();\n  const innerParams = privateProps.innerParams.get(this);\n  if (!popup || hasClass(popup, innerParams.hideClass.popup)) {\n    warn(`You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.`);\n    return;\n  }\n  const validUpdatableParams = filterValidParams(params);\n  const updatedParams = Object.assign({}, innerParams, validUpdatableParams);\n  showWarningsForParams(updatedParams);\n  container.dataset['swal2Theme'] = updatedParams.theme;\n  render(this, updatedParams);\n  privateProps.innerParams.set(this, updatedParams);\n  Object.defineProperties(this, {\n    params: {\n      value: Object.assign({}, this.params, params),\n      writable: false,\n      enumerable: true\n    }\n  });\n}\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {SweetAlertOptions}\n */\nconst filterValidParams = params => {\n  const validUpdatableParams = {};\n  Object.keys(params).forEach(param => {\n    if (isUpdatableParameter(param)) {\n      validUpdatableParams[param] = params[param];\n    } else {\n      warn(`Invalid parameter to update: ${param}`);\n    }\n  });\n  return validUpdatableParams;\n};\n\n/**\n * Dispose the current SweetAlert2 instance\n */\nfunction _destroy() {\n  const domCache = privateProps.domCache.get(this);\n  const innerParams = privateProps.innerParams.get(this);\n  if (!innerParams) {\n    disposeWeakMaps(this); // The WeakMaps might have been partly destroyed, we must recall it to dispose any remaining WeakMaps #2335\n    return; // This instance has already been destroyed\n  }\n\n  // Check if there is another Swal closing\n  if (domCache.popup && globalState.swalCloseEventFinishedCallback) {\n    globalState.swalCloseEventFinishedCallback();\n    delete globalState.swalCloseEventFinishedCallback;\n  }\n  if (typeof innerParams.didDestroy === 'function') {\n    innerParams.didDestroy();\n  }\n  globalState.eventEmitter.emit('didDestroy');\n  disposeSwal(this);\n}\n\n/**\n * @param {SweetAlert} instance\n */\nconst disposeSwal = instance => {\n  disposeWeakMaps(instance);\n  // Unset this.params so GC will dispose it (#1569)\n  delete instance.params;\n  // Unset globalState props so GC will dispose globalState (#1569)\n  delete globalState.keydownHandler;\n  delete globalState.keydownTarget;\n  // Unset currentInstance\n  delete globalState.currentInstance;\n};\n\n/**\n * @param {SweetAlert} instance\n */\nconst disposeWeakMaps = instance => {\n  // If the current instance is awaiting a promise result, we keep the privateMethods to call them once the promise result is retrieved #2335\n  if (instance.isAwaitingPromise) {\n    unsetWeakMaps(privateProps, instance);\n    instance.isAwaitingPromise = true;\n  } else {\n    unsetWeakMaps(privateMethods, instance);\n    unsetWeakMaps(privateProps, instance);\n    delete instance.isAwaitingPromise;\n    // Unset instance methods\n    delete instance.disableButtons;\n    delete instance.enableButtons;\n    delete instance.getInput;\n    delete instance.disableInput;\n    delete instance.enableInput;\n    delete instance.hideLoading;\n    delete instance.disableLoading;\n    delete instance.showValidationMessage;\n    delete instance.resetValidationMessage;\n    delete instance.close;\n    delete instance.closePopup;\n    delete instance.closeModal;\n    delete instance.closeToast;\n    delete instance.rejectPromise;\n    delete instance.update;\n    delete instance._destroy;\n  }\n};\n\n/**\n * @param {object} obj\n * @param {SweetAlert} instance\n */\nconst unsetWeakMaps = (obj, instance) => {\n  for (const i in obj) {\n    obj[i].delete(instance);\n  }\n};\n\nvar instanceMethods = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  _destroy: _destroy,\n  close: close,\n  closeModal: close,\n  closePopup: close,\n  closeToast: close,\n  disableButtons: disableButtons,\n  disableInput: disableInput,\n  disableLoading: hideLoading,\n  enableButtons: enableButtons,\n  enableInput: enableInput,\n  getInput: getInput,\n  handleAwaitingPromise: handleAwaitingPromise,\n  hideLoading: hideLoading,\n  rejectPromise: rejectPromise,\n  resetValidationMessage: resetValidationMessage,\n  showValidationMessage: showValidationMessage,\n  update: update\n});\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handlePopupClick = (innerParams, domCache, dismissWith) => {\n  if (innerParams.toast) {\n    handleToastClick(innerParams, domCache, dismissWith);\n  } else {\n    // Ignore click events that had mousedown on the popup but mouseup on the container\n    // This can happen when the user drags a slider\n    handleModalMousedown(domCache);\n\n    // Ignore click events that had mousedown on the container but mouseup on the popup\n    handleContainerMousedown(domCache);\n    handleModalClick(innerParams, domCache, dismissWith);\n  }\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handleToastClick = (innerParams, domCache, dismissWith) => {\n  // Closing toast by internal click\n  domCache.popup.onclick = () => {\n    if (innerParams && (isAnyButtonShown(innerParams) || innerParams.timer || innerParams.input)) {\n      return;\n    }\n    dismissWith(DismissReason.close);\n  };\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @returns {boolean}\n */\nconst isAnyButtonShown = innerParams => {\n  return !!(innerParams.showConfirmButton || innerParams.showDenyButton || innerParams.showCancelButton || innerParams.showCloseButton);\n};\nlet ignoreOutsideClick = false;\n\n/**\n * @param {DomCache} domCache\n */\nconst handleModalMousedown = domCache => {\n  domCache.popup.onmousedown = () => {\n    domCache.container.onmouseup = function (e) {\n      domCache.container.onmouseup = () => {};\n      // We only check if the mouseup target is the container because usually it doesn't\n      // have any other direct children aside of the popup\n      if (e.target === domCache.container) {\n        ignoreOutsideClick = true;\n      }\n    };\n  };\n};\n\n/**\n * @param {DomCache} domCache\n */\nconst handleContainerMousedown = domCache => {\n  domCache.container.onmousedown = e => {\n    // prevent the modal text from being selected on double click on the container (allowOutsideClick: false)\n    if (e.target === domCache.container) {\n      e.preventDefault();\n    }\n    domCache.popup.onmouseup = function (e) {\n      domCache.popup.onmouseup = () => {};\n      // We also need to check if the mouseup target is a child of the popup\n      if (e.target === domCache.popup || e.target instanceof HTMLElement && domCache.popup.contains(e.target)) {\n        ignoreOutsideClick = true;\n      }\n    };\n  };\n};\n\n/**\n * @param {SweetAlertOptions} innerParams\n * @param {DomCache} domCache\n * @param {Function} dismissWith\n */\nconst handleModalClick = (innerParams, domCache, dismissWith) => {\n  domCache.container.onclick = e => {\n    if (ignoreOutsideClick) {\n      ignoreOutsideClick = false;\n      return;\n    }\n    if (e.target === domCache.container && callIfFunction(innerParams.allowOutsideClick)) {\n      dismissWith(DismissReason.backdrop);\n    }\n  };\n};\n\nconst isJqueryElement = elem => typeof elem === 'object' && elem.jquery;\nconst isElement = elem => elem instanceof Element || isJqueryElement(elem);\nconst argsToParams = args => {\n  const params = {};\n  if (typeof args[0] === 'object' && !isElement(args[0])) {\n    Object.assign(params, args[0]);\n  } else {\n    ['title', 'html', 'icon'].forEach((name, index) => {\n      const arg = args[index];\n      if (typeof arg === 'string' || isElement(arg)) {\n        params[name] = arg;\n      } else if (arg !== undefined) {\n        error(`Unexpected type of ${name}! Expected \"string\" or \"Element\", got ${typeof arg}`);\n      }\n    });\n  }\n  return params;\n};\n\n/**\n * Main method to create a new SweetAlert2 popup\n *\n * @param  {...SweetAlertOptions} args\n * @returns {Promise<SweetAlertResult>}\n */\nfunction fire(...args) {\n  return new this(...args);\n}\n\n/**\n * Returns an extended version of `Swal` containing `params` as defaults.\n * Useful for reusing Swal configuration.\n *\n * For example:\n *\n * Before:\n * const textPromptOptions = { input: 'text', showCancelButton: true }\n * const {value: firstName} = await Swal.fire({ ...textPromptOptions, title: 'What is your first name?' })\n * const {value: lastName} = await Swal.fire({ ...textPromptOptions, title: 'What is your last name?' })\n *\n * After:\n * const TextPrompt = Swal.mixin({ input: 'text', showCancelButton: true })\n * const {value: firstName} = await TextPrompt('What is your first name?')\n * const {value: lastName} = await TextPrompt('What is your last name?')\n *\n * @param {SweetAlertOptions} mixinParams\n * @returns {SweetAlert}\n */\nfunction mixin(mixinParams) {\n  class MixinSwal extends this {\n    _main(params, priorityMixinParams) {\n      return super._main(params, Object.assign({}, mixinParams, priorityMixinParams));\n    }\n  }\n  // @ts-ignore\n  return MixinSwal;\n}\n\n/**\n * If `timer` parameter is set, returns number of milliseconds of timer remained.\n * Otherwise, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst getTimerLeft = () => {\n  return globalState.timeout && globalState.timeout.getTimerLeft();\n};\n\n/**\n * Stop timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst stopTimer = () => {\n  if (globalState.timeout) {\n    stopTimerProgressBar();\n    return globalState.timeout.stop();\n  }\n};\n\n/**\n * Resume timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst resumeTimer = () => {\n  if (globalState.timeout) {\n    const remaining = globalState.timeout.start();\n    animateTimerProgressBar(remaining);\n    return remaining;\n  }\n};\n\n/**\n * Resume timer. Returns number of milliseconds of timer remained.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @returns {number | undefined}\n */\nconst toggleTimer = () => {\n  const timer = globalState.timeout;\n  return timer && (timer.running ? stopTimer() : resumeTimer());\n};\n\n/**\n * Increase timer. Returns number of milliseconds of an updated timer.\n * If `timer` parameter isn't set, returns undefined.\n *\n * @param {number} ms\n * @returns {number | undefined}\n */\nconst increaseTimer = ms => {\n  if (globalState.timeout) {\n    const remaining = globalState.timeout.increase(ms);\n    animateTimerProgressBar(remaining, true);\n    return remaining;\n  }\n};\n\n/**\n * Check if timer is running. Returns true if timer is running\n * or false if timer is paused or stopped.\n * If `timer` parameter isn't set, returns undefined\n *\n * @returns {boolean}\n */\nconst isTimerRunning = () => {\n  return !!(globalState.timeout && globalState.timeout.isRunning());\n};\n\nlet bodyClickListenerAdded = false;\nconst clickHandlers = {};\n\n/**\n * @param {string} attr\n */\nfunction bindClickHandler(attr = 'data-swal-template') {\n  clickHandlers[attr] = this;\n  if (!bodyClickListenerAdded) {\n    document.body.addEventListener('click', bodyClickListener);\n    bodyClickListenerAdded = true;\n  }\n}\nconst bodyClickListener = event => {\n  for (let el = event.target; el && el !== document; el = el.parentNode) {\n    for (const attr in clickHandlers) {\n      const template = el.getAttribute(attr);\n      if (template) {\n        clickHandlers[attr].fire({\n          template\n        });\n        return;\n      }\n    }\n  }\n};\n\n// Source: https://gist.github.com/mudge/5830382?permalink_comment_id=2691957#gistcomment-2691957\n\nclass EventEmitter {\n  constructor() {\n    /** @type {Events} */\n    this.events = {};\n  }\n\n  /**\n   * @param {string} eventName\n   * @returns {EventHandlers}\n   */\n  _getHandlersByEventName(eventName) {\n    if (typeof this.events[eventName] === 'undefined') {\n      // not Set because we need to keep the FIFO order\n      // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1748990334\n      this.events[eventName] = [];\n    }\n    return this.events[eventName];\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  on(eventName, eventHandler) {\n    const currentHandlers = this._getHandlersByEventName(eventName);\n    if (!currentHandlers.includes(eventHandler)) {\n      currentHandlers.push(eventHandler);\n    }\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  once(eventName, eventHandler) {\n    /**\n     * @param {Array} args\n     */\n    const onceFn = (...args) => {\n      this.removeListener(eventName, onceFn);\n      eventHandler.apply(this, args);\n    };\n    this.on(eventName, onceFn);\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {Array} args\n   */\n  emit(eventName, ...args) {\n    this._getHandlersByEventName(eventName).forEach(\n    /**\n     * @param {EventHandler} eventHandler\n     */\n    eventHandler => {\n      try {\n        eventHandler.apply(this, args);\n      } catch (error) {\n        console.error(error);\n      }\n    });\n  }\n\n  /**\n   * @param {string} eventName\n   * @param {EventHandler} eventHandler\n   */\n  removeListener(eventName, eventHandler) {\n    const currentHandlers = this._getHandlersByEventName(eventName);\n    const index = currentHandlers.indexOf(eventHandler);\n    if (index > -1) {\n      currentHandlers.splice(index, 1);\n    }\n  }\n\n  /**\n   * @param {string} eventName\n   */\n  removeAllListeners(eventName) {\n    if (this.events[eventName] !== undefined) {\n      // https://github.com/sweetalert2/sweetalert2/pull/2763#discussion_r1749239222\n      this.events[eventName].length = 0;\n    }\n  }\n  reset() {\n    this.events = {};\n  }\n}\n\nglobalState.eventEmitter = new EventEmitter();\n\n/**\n * @param {string} eventName\n * @param {EventHandler} eventHandler\n */\nconst on = (eventName, eventHandler) => {\n  globalState.eventEmitter.on(eventName, eventHandler);\n};\n\n/**\n * @param {string} eventName\n * @param {EventHandler} eventHandler\n */\nconst once = (eventName, eventHandler) => {\n  globalState.eventEmitter.once(eventName, eventHandler);\n};\n\n/**\n * @param {string} [eventName]\n * @param {EventHandler} [eventHandler]\n */\nconst off = (eventName, eventHandler) => {\n  // Remove all handlers for all events\n  if (!eventName) {\n    globalState.eventEmitter.reset();\n    return;\n  }\n  if (eventHandler) {\n    // Remove a specific handler\n    globalState.eventEmitter.removeListener(eventName, eventHandler);\n  } else {\n    // Remove all handlers for a specific event\n    globalState.eventEmitter.removeAllListeners(eventName);\n  }\n};\n\nvar staticMethods = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  argsToParams: argsToParams,\n  bindClickHandler: bindClickHandler,\n  clickCancel: clickCancel,\n  clickConfirm: clickConfirm,\n  clickDeny: clickDeny,\n  enableLoading: showLoading,\n  fire: fire,\n  getActions: getActions,\n  getCancelButton: getCancelButton,\n  getCloseButton: getCloseButton,\n  getConfirmButton: getConfirmButton,\n  getContainer: getContainer,\n  getDenyButton: getDenyButton,\n  getFocusableElements: getFocusableElements,\n  getFooter: getFooter,\n  getHtmlContainer: getHtmlContainer,\n  getIcon: getIcon,\n  getIconContent: getIconContent,\n  getImage: getImage,\n  getInputLabel: getInputLabel,\n  getLoader: getLoader,\n  getPopup: getPopup,\n  getProgressSteps: getProgressSteps,\n  getTimerLeft: getTimerLeft,\n  getTimerProgressBar: getTimerProgressBar,\n  getTitle: getTitle,\n  getValidationMessage: getValidationMessage,\n  increaseTimer: increaseTimer,\n  isDeprecatedParameter: isDeprecatedParameter,\n  isLoading: isLoading,\n  isTimerRunning: isTimerRunning,\n  isUpdatableParameter: isUpdatableParameter,\n  isValidParameter: isValidParameter,\n  isVisible: isVisible,\n  mixin: mixin,\n  off: off,\n  on: on,\n  once: once,\n  resumeTimer: resumeTimer,\n  showLoading: showLoading,\n  stopTimer: stopTimer,\n  toggleTimer: toggleTimer\n});\n\nclass Timer {\n  /**\n   * @param {Function} callback\n   * @param {number} delay\n   */\n  constructor(callback, delay) {\n    this.callback = callback;\n    this.remaining = delay;\n    this.running = false;\n    this.start();\n  }\n\n  /**\n   * @returns {number}\n   */\n  start() {\n    if (!this.running) {\n      this.running = true;\n      this.started = new Date();\n      this.id = setTimeout(this.callback, this.remaining);\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {number}\n   */\n  stop() {\n    if (this.started && this.running) {\n      this.running = false;\n      clearTimeout(this.id);\n      this.remaining -= new Date().getTime() - this.started.getTime();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @param {number} n\n   * @returns {number}\n   */\n  increase(n) {\n    const running = this.running;\n    if (running) {\n      this.stop();\n    }\n    this.remaining += n;\n    if (running) {\n      this.start();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {number}\n   */\n  getTimerLeft() {\n    if (this.running) {\n      this.stop();\n      this.start();\n    }\n    return this.remaining;\n  }\n\n  /**\n   * @returns {boolean}\n   */\n  isRunning() {\n    return this.running;\n  }\n}\n\nconst swalStringParams = ['swal-title', 'swal-html', 'swal-footer'];\n\n/**\n * @param {SweetAlertOptions} params\n * @returns {SweetAlertOptions}\n */\nconst getTemplateParams = params => {\n  const template = typeof params.template === 'string' ? (/** @type {HTMLTemplateElement} */document.querySelector(params.template)) : params.template;\n  if (!template) {\n    return {};\n  }\n  /** @type {DocumentFragment} */\n  const templateContent = template.content;\n  showWarningsForElements(templateContent);\n  const result = Object.assign(getSwalParams(templateContent), getSwalFunctionParams(templateContent), getSwalButtons(templateContent), getSwalImage(templateContent), getSwalIcon(templateContent), getSwalInput(templateContent), getSwalStringParams(templateContent, swalStringParams));\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalParams = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalParams = Array.from(templateContent.querySelectorAll('swal-param'));\n  swalParams.forEach(param => {\n    showWarningsForAttributes(param, ['name', 'value']);\n    const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n    const value = param.getAttribute('value');\n    if (!paramName || !value) {\n      return;\n    }\n    if (typeof defaultParams[paramName] === 'boolean') {\n      result[paramName] = value !== 'false';\n    } else if (typeof defaultParams[paramName] === 'object') {\n      result[paramName] = JSON.parse(value);\n    } else {\n      result[paramName] = value;\n    }\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalFunctionParams = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalFunctions = Array.from(templateContent.querySelectorAll('swal-function-param'));\n  swalFunctions.forEach(param => {\n    const paramName = /** @type {keyof SweetAlertOptions} */param.getAttribute('name');\n    const value = param.getAttribute('value');\n    if (!paramName || !value) {\n      return;\n    }\n    result[paramName] = new Function(`return ${value}`)();\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalButtons = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement[]} */\n  const swalButtons = Array.from(templateContent.querySelectorAll('swal-button'));\n  swalButtons.forEach(button => {\n    showWarningsForAttributes(button, ['type', 'color', 'aria-label']);\n    const type = button.getAttribute('type');\n    if (!type || !['confirm', 'cancel', 'deny'].includes(type)) {\n      return;\n    }\n    result[`${type}ButtonText`] = button.innerHTML;\n    result[`show${capitalizeFirstLetter(type)}Button`] = true;\n    if (button.hasAttribute('color')) {\n      result[`${type}ButtonColor`] = button.getAttribute('color');\n    }\n    if (button.hasAttribute('aria-label')) {\n      result[`${type}ButtonAriaLabel`] = button.getAttribute('aria-label');\n    }\n  });\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Pick<SweetAlertOptions, 'imageUrl' | 'imageWidth' | 'imageHeight' | 'imageAlt'>}\n */\nconst getSwalImage = templateContent => {\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const image = templateContent.querySelector('swal-image');\n  if (image) {\n    showWarningsForAttributes(image, ['src', 'width', 'height', 'alt']);\n    if (image.hasAttribute('src')) {\n      result.imageUrl = image.getAttribute('src') || undefined;\n    }\n    if (image.hasAttribute('width')) {\n      result.imageWidth = image.getAttribute('width') || undefined;\n    }\n    if (image.hasAttribute('height')) {\n      result.imageHeight = image.getAttribute('height') || undefined;\n    }\n    if (image.hasAttribute('alt')) {\n      result.imageAlt = image.getAttribute('alt') || undefined;\n    }\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalIcon = templateContent => {\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const icon = templateContent.querySelector('swal-icon');\n  if (icon) {\n    showWarningsForAttributes(icon, ['type', 'color']);\n    if (icon.hasAttribute('type')) {\n      result.icon = icon.getAttribute('type');\n    }\n    if (icon.hasAttribute('color')) {\n      result.iconColor = icon.getAttribute('color');\n    }\n    result.iconHtml = icon.innerHTML;\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @returns {Record<string, any>}\n */\nconst getSwalInput = templateContent => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  /** @type {HTMLElement | null} */\n  const input = templateContent.querySelector('swal-input');\n  if (input) {\n    showWarningsForAttributes(input, ['type', 'label', 'placeholder', 'value']);\n    result.input = input.getAttribute('type') || 'text';\n    if (input.hasAttribute('label')) {\n      result.inputLabel = input.getAttribute('label');\n    }\n    if (input.hasAttribute('placeholder')) {\n      result.inputPlaceholder = input.getAttribute('placeholder');\n    }\n    if (input.hasAttribute('value')) {\n      result.inputValue = input.getAttribute('value');\n    }\n  }\n  /** @type {HTMLElement[]} */\n  const inputOptions = Array.from(templateContent.querySelectorAll('swal-input-option'));\n  if (inputOptions.length) {\n    result.inputOptions = {};\n    inputOptions.forEach(option => {\n      showWarningsForAttributes(option, ['value']);\n      const optionValue = option.getAttribute('value');\n      if (!optionValue) {\n        return;\n      }\n      const optionName = option.innerHTML;\n      result.inputOptions[optionValue] = optionName;\n    });\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n * @param {string[]} paramNames\n * @returns {Record<string, any>}\n */\nconst getSwalStringParams = (templateContent, paramNames) => {\n  /** @type {Record<string, any>} */\n  const result = {};\n  for (const i in paramNames) {\n    const paramName = paramNames[i];\n    /** @type {HTMLElement | null} */\n    const tag = templateContent.querySelector(paramName);\n    if (tag) {\n      showWarningsForAttributes(tag, []);\n      result[paramName.replace(/^swal-/, '')] = tag.innerHTML.trim();\n    }\n  }\n  return result;\n};\n\n/**\n * @param {DocumentFragment} templateContent\n */\nconst showWarningsForElements = templateContent => {\n  const allowedElements = swalStringParams.concat(['swal-param', 'swal-function-param', 'swal-button', 'swal-image', 'swal-icon', 'swal-input', 'swal-input-option']);\n  Array.from(templateContent.children).forEach(el => {\n    const tagName = el.tagName.toLowerCase();\n    if (!allowedElements.includes(tagName)) {\n      warn(`Unrecognized element <${tagName}>`);\n    }\n  });\n};\n\n/**\n * @param {HTMLElement} el\n * @param {string[]} allowedAttributes\n */\nconst showWarningsForAttributes = (el, allowedAttributes) => {\n  Array.from(el.attributes).forEach(attribute => {\n    if (allowedAttributes.indexOf(attribute.name) === -1) {\n      warn([`Unrecognized attribute \"${attribute.name}\" on <${el.tagName.toLowerCase()}>.`, `${allowedAttributes.length ? `Allowed attributes are: ${allowedAttributes.join(', ')}` : 'To set the value, use HTML within the element.'}`]);\n    }\n  });\n};\n\nconst SHOW_CLASS_TIMEOUT = 10;\n\n/**\n * Open popup, add necessary classes and styles, fix scrollbar\n *\n * @param {SweetAlertOptions} params\n */\nconst openPopup = params => {\n  const container = getContainer();\n  const popup = getPopup();\n  if (typeof params.willOpen === 'function') {\n    params.willOpen(popup);\n  }\n  globalState.eventEmitter.emit('willOpen', popup);\n  const bodyStyles = window.getComputedStyle(document.body);\n  const initialBodyOverflow = bodyStyles.overflowY;\n  addClasses(container, popup, params);\n\n  // scrolling is 'hidden' until animation is done, after that 'auto'\n  setTimeout(() => {\n    setScrollingVisibility(container, popup);\n  }, SHOW_CLASS_TIMEOUT);\n  if (isModal()) {\n    fixScrollContainer(container, params.scrollbarPadding, initialBodyOverflow);\n    setAriaHidden();\n  }\n  if (!isToast() && !globalState.previousActiveElement) {\n    globalState.previousActiveElement = document.activeElement;\n  }\n  if (typeof params.didOpen === 'function') {\n    setTimeout(() => params.didOpen(popup));\n  }\n  globalState.eventEmitter.emit('didOpen', popup);\n  removeClass(container, swalClasses['no-transition']);\n};\n\n/**\n * @param {AnimationEvent} event\n */\nconst swalOpenAnimationFinished = event => {\n  const popup = getPopup();\n  if (event.target !== popup) {\n    return;\n  }\n  const container = getContainer();\n  popup.removeEventListener('animationend', swalOpenAnimationFinished);\n  popup.removeEventListener('transitionend', swalOpenAnimationFinished);\n  container.style.overflowY = 'auto';\n};\n\n/**\n * @param {HTMLElement} container\n * @param {HTMLElement} popup\n */\nconst setScrollingVisibility = (container, popup) => {\n  if (hasCssAnimation(popup)) {\n    container.style.overflowY = 'hidden';\n    popup.addEventListener('animationend', swalOpenAnimationFinished);\n    popup.addEventListener('transitionend', swalOpenAnimationFinished);\n  } else {\n    container.style.overflowY = 'auto';\n  }\n};\n\n/**\n * @param {HTMLElement} container\n * @param {boolean} scrollbarPadding\n * @param {string} initialBodyOverflow\n */\nconst fixScrollContainer = (container, scrollbarPadding, initialBodyOverflow) => {\n  iOSfix();\n  if (scrollbarPadding && initialBodyOverflow !== 'hidden') {\n    replaceScrollbarWithPadding(initialBodyOverflow);\n  }\n\n  // sweetalert2/issues/1247\n  setTimeout(() => {\n    container.scrollTop = 0;\n  });\n};\n\n/**\n * @param {HTMLElement} container\n * @param {HTMLElement} popup\n * @param {SweetAlertOptions} params\n */\nconst addClasses = (container, popup, params) => {\n  addClass(container, params.showClass.backdrop);\n  if (params.animation) {\n    // this workaround with opacity is needed for https://github.com/sweetalert2/sweetalert2/issues/2059\n    popup.style.setProperty('opacity', '0', 'important');\n    show(popup, 'grid');\n    setTimeout(() => {\n      // Animate popup right after showing it\n      addClass(popup, params.showClass.popup);\n      // and remove the opacity workaround\n      popup.style.removeProperty('opacity');\n    }, SHOW_CLASS_TIMEOUT); // 10ms in order to fix #2062\n  } else {\n    show(popup, 'grid');\n  }\n  addClass([document.documentElement, document.body], swalClasses.shown);\n  if (params.heightAuto && params.backdrop && !params.toast) {\n    addClass([document.documentElement, document.body], swalClasses['height-auto']);\n  }\n};\n\nvar defaultInputValidators = {\n  /**\n   * @param {string} string\n   * @param {string} [validationMessage]\n   * @returns {Promise<string | void>}\n   */\n  email: (string, validationMessage) => {\n    return /^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z0-9-]+$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid email address');\n  },\n  /**\n   * @param {string} string\n   * @param {string} [validationMessage]\n   * @returns {Promise<string | void>}\n   */\n  url: (string, validationMessage) => {\n    // taken from https://stackoverflow.com/a/3809435 with a small change from #1306 and #2013\n    return /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-z]{2,63}\\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(string) ? Promise.resolve() : Promise.resolve(validationMessage || 'Invalid URL');\n  }\n};\n\n/**\n * @param {SweetAlertOptions} params\n */\nfunction setDefaultInputValidators(params) {\n  // Use default `inputValidator` for supported input types if not provided\n  if (params.inputValidator) {\n    return;\n  }\n  if (params.input === 'email') {\n    params.inputValidator = defaultInputValidators['email'];\n  }\n  if (params.input === 'url') {\n    params.inputValidator = defaultInputValidators['url'];\n  }\n}\n\n/**\n * @param {SweetAlertOptions} params\n */\nfunction validateCustomTargetElement(params) {\n  // Determine if the custom target element is valid\n  if (!params.target || typeof params.target === 'string' && !document.querySelector(params.target) || typeof params.target !== 'string' && !params.target.appendChild) {\n    warn('Target parameter is not valid, defaulting to \"body\"');\n    params.target = 'body';\n  }\n}\n\n/**\n * Set type, text and actions on popup\n *\n * @param {SweetAlertOptions} params\n */\nfunction setParameters(params) {\n  setDefaultInputValidators(params);\n\n  // showLoaderOnConfirm && preConfirm\n  if (params.showLoaderOnConfirm && !params.preConfirm) {\n    warn('showLoaderOnConfirm is set to true, but preConfirm is not defined.\\n' + 'showLoaderOnConfirm should be used together with preConfirm, see usage example:\\n' + 'https://sweetalert2.github.io/#ajax-request');\n  }\n  validateCustomTargetElement(params);\n\n  // Replace newlines with <br> in title\n  if (typeof params.title === 'string') {\n    params.title = params.title.split('\\n').join('<br />');\n  }\n  init(params);\n}\n\n/** @type {SweetAlert} */\nlet currentInstance;\nvar _promise = /*#__PURE__*/new WeakMap();\nclass SweetAlert {\n  /**\n   * @param {...any} args\n   * @this {SweetAlert}\n   */\n  constructor(...args) {\n    /**\n     * @type {Promise<SweetAlertResult>}\n     */\n    _classPrivateFieldInitSpec(this, _promise, void 0);\n    // Prevent run in Node env\n    if (typeof window === 'undefined') {\n      return;\n    }\n    currentInstance = this;\n\n    // @ts-ignore\n    const outerParams = Object.freeze(this.constructor.argsToParams(args));\n\n    /** @type {Readonly<SweetAlertOptions>} */\n    this.params = outerParams;\n\n    /** @type {boolean} */\n    this.isAwaitingPromise = false;\n    _classPrivateFieldSet2(_promise, this, this._main(currentInstance.params));\n  }\n  _main(userParams, mixinParams = {}) {\n    showWarningsForParams(Object.assign({}, mixinParams, userParams));\n    if (globalState.currentInstance) {\n      const swalPromiseResolve = privateMethods.swalPromiseResolve.get(globalState.currentInstance);\n      const {\n        isAwaitingPromise\n      } = globalState.currentInstance;\n      globalState.currentInstance._destroy();\n      if (!isAwaitingPromise) {\n        swalPromiseResolve({\n          isDismissed: true\n        });\n      }\n      if (isModal()) {\n        unsetAriaHidden();\n      }\n    }\n    globalState.currentInstance = currentInstance;\n    const innerParams = prepareParams(userParams, mixinParams);\n    setParameters(innerParams);\n    Object.freeze(innerParams);\n\n    // clear the previous timer\n    if (globalState.timeout) {\n      globalState.timeout.stop();\n      delete globalState.timeout;\n    }\n\n    // clear the restore focus timeout\n    clearTimeout(globalState.restoreFocusTimeout);\n    const domCache = populateDomCache(currentInstance);\n    render(currentInstance, innerParams);\n    privateProps.innerParams.set(currentInstance, innerParams);\n    return swalPromise(currentInstance, domCache, innerParams);\n  }\n\n  // `catch` cannot be the name of a module export, so we define our thenable methods here instead\n  then(onFulfilled) {\n    return _classPrivateFieldGet2(_promise, this).then(onFulfilled);\n  }\n  finally(onFinally) {\n    return _classPrivateFieldGet2(_promise, this).finally(onFinally);\n  }\n}\n\n/**\n * @param {SweetAlert} instance\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n * @returns {Promise}\n */\nconst swalPromise = (instance, domCache, innerParams) => {\n  return new Promise((resolve, reject) => {\n    // functions to handle all closings/dismissals\n    /**\n     * @param {DismissReason} dismiss\n     */\n    const dismissWith = dismiss => {\n      instance.close({\n        isDismissed: true,\n        dismiss\n      });\n    };\n    privateMethods.swalPromiseResolve.set(instance, resolve);\n    privateMethods.swalPromiseReject.set(instance, reject);\n    domCache.confirmButton.onclick = () => {\n      handleConfirmButtonClick(instance);\n    };\n    domCache.denyButton.onclick = () => {\n      handleDenyButtonClick(instance);\n    };\n    domCache.cancelButton.onclick = () => {\n      handleCancelButtonClick(instance, dismissWith);\n    };\n    domCache.closeButton.onclick = () => {\n      dismissWith(DismissReason.close);\n    };\n    handlePopupClick(innerParams, domCache, dismissWith);\n    addKeydownHandler(globalState, innerParams, dismissWith);\n    handleInputOptionsAndValue(instance, innerParams);\n    openPopup(innerParams);\n    setupTimer(globalState, innerParams, dismissWith);\n    initFocus(domCache, innerParams);\n\n    // Scroll container to top on open (#1247, #1946)\n    setTimeout(() => {\n      domCache.container.scrollTop = 0;\n    });\n  });\n};\n\n/**\n * @param {SweetAlertOptions} userParams\n * @param {SweetAlertOptions} mixinParams\n * @returns {SweetAlertOptions}\n */\nconst prepareParams = (userParams, mixinParams) => {\n  const templateParams = getTemplateParams(userParams);\n  const params = Object.assign({}, defaultParams, mixinParams, templateParams, userParams); // precedence is described in #2131\n  params.showClass = Object.assign({}, defaultParams.showClass, params.showClass);\n  params.hideClass = Object.assign({}, defaultParams.hideClass, params.hideClass);\n  if (params.animation === false) {\n    params.showClass = {\n      backdrop: 'swal2-noanimation'\n    };\n    params.hideClass = {};\n  }\n  return params;\n};\n\n/**\n * @param {SweetAlert} instance\n * @returns {DomCache}\n */\nconst populateDomCache = instance => {\n  const domCache = {\n    popup: getPopup(),\n    container: getContainer(),\n    actions: getActions(),\n    confirmButton: getConfirmButton(),\n    denyButton: getDenyButton(),\n    cancelButton: getCancelButton(),\n    loader: getLoader(),\n    closeButton: getCloseButton(),\n    validationMessage: getValidationMessage(),\n    progressSteps: getProgressSteps()\n  };\n  privateProps.domCache.set(instance, domCache);\n  return domCache;\n};\n\n/**\n * @param {GlobalState} globalState\n * @param {SweetAlertOptions} innerParams\n * @param {Function} dismissWith\n */\nconst setupTimer = (globalState, innerParams, dismissWith) => {\n  const timerProgressBar = getTimerProgressBar();\n  hide(timerProgressBar);\n  if (innerParams.timer) {\n    globalState.timeout = new Timer(() => {\n      dismissWith('timer');\n      delete globalState.timeout;\n    }, innerParams.timer);\n    if (innerParams.timerProgressBar) {\n      show(timerProgressBar);\n      applyCustomClass(timerProgressBar, innerParams, 'timerProgressBar');\n      setTimeout(() => {\n        if (globalState.timeout && globalState.timeout.running) {\n          // timer can be already stopped or unset at this point\n          animateTimerProgressBar(innerParams.timer);\n        }\n      });\n    }\n  }\n};\n\n/**\n * Initialize focus in the popup:\n *\n * 1. If `toast` is `true`, don't steal focus from the document.\n * 2. Else if there is an [autofocus] element, focus it.\n * 3. Else if `focusConfirm` is `true` and confirm button is visible, focus it.\n * 4. Else if `focusDeny` is `true` and deny button is visible, focus it.\n * 5. Else if `focusCancel` is `true` and cancel button is visible, focus it.\n * 6. Else focus the first focusable element in a popup (if any).\n *\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n */\nconst initFocus = (domCache, innerParams) => {\n  if (innerParams.toast) {\n    return;\n  }\n  // TODO: this is dumb, remove `allowEnterKey` param in the next major version\n  if (!callIfFunction(innerParams.allowEnterKey)) {\n    warnAboutDeprecation('allowEnterKey');\n    blurActiveElement();\n    return;\n  }\n  if (focusAutofocus(domCache)) {\n    return;\n  }\n  if (focusButton(domCache, innerParams)) {\n    return;\n  }\n  setFocus(-1, 1);\n};\n\n/**\n * @param {DomCache} domCache\n * @returns {boolean}\n */\nconst focusAutofocus = domCache => {\n  const autofocusElements = Array.from(domCache.popup.querySelectorAll('[autofocus]'));\n  for (const autofocusElement of autofocusElements) {\n    if (autofocusElement instanceof HTMLElement && isVisible$1(autofocusElement)) {\n      autofocusElement.focus();\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * @param {DomCache} domCache\n * @param {SweetAlertOptions} innerParams\n * @returns {boolean}\n */\nconst focusButton = (domCache, innerParams) => {\n  if (innerParams.focusDeny && isVisible$1(domCache.denyButton)) {\n    domCache.denyButton.focus();\n    return true;\n  }\n  if (innerParams.focusCancel && isVisible$1(domCache.cancelButton)) {\n    domCache.cancelButton.focus();\n    return true;\n  }\n  if (innerParams.focusConfirm && isVisible$1(domCache.confirmButton)) {\n    domCache.confirmButton.focus();\n    return true;\n  }\n  return false;\n};\nconst blurActiveElement = () => {\n  if (document.activeElement instanceof HTMLElement && typeof document.activeElement.blur === 'function') {\n    document.activeElement.blur();\n  }\n};\n\n// Dear russian users visiting russian sites. Let's have fun.\nif (typeof window !== 'undefined' && /^ru\\b/.test(navigator.language) && location.host.match(/\\.(ru|su|by|xn--p1ai)$/)) {\n  const now = new Date();\n  const initiationDate = localStorage.getItem('swal-initiation');\n  if (!initiationDate) {\n    localStorage.setItem('swal-initiation', `${now}`);\n  } else if ((now.getTime() - Date.parse(initiationDate)) / (1000 * 60 * 60 * 24) > 3) {\n    setTimeout(() => {\n      document.body.style.pointerEvents = 'none';\n      const ukrainianAnthem = document.createElement('audio');\n      ukrainianAnthem.src = 'https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3';\n      ukrainianAnthem.loop = true;\n      document.body.appendChild(ukrainianAnthem);\n      setTimeout(() => {\n        ukrainianAnthem.play().catch(() => {\n          // ignore\n        });\n      }, 2500);\n    }, 500);\n  }\n}\n\n// Assign instance methods from src/instanceMethods/*.js to prototype\nSweetAlert.prototype.disableButtons = disableButtons;\nSweetAlert.prototype.enableButtons = enableButtons;\nSweetAlert.prototype.getInput = getInput;\nSweetAlert.prototype.disableInput = disableInput;\nSweetAlert.prototype.enableInput = enableInput;\nSweetAlert.prototype.hideLoading = hideLoading;\nSweetAlert.prototype.disableLoading = hideLoading;\nSweetAlert.prototype.showValidationMessage = showValidationMessage;\nSweetAlert.prototype.resetValidationMessage = resetValidationMessage;\nSweetAlert.prototype.close = close;\nSweetAlert.prototype.closePopup = close;\nSweetAlert.prototype.closeModal = close;\nSweetAlert.prototype.closeToast = close;\nSweetAlert.prototype.rejectPromise = rejectPromise;\nSweetAlert.prototype.update = update;\nSweetAlert.prototype._destroy = _destroy;\n\n// Assign static methods from src/staticMethods/*.js to constructor\nObject.assign(SweetAlert, staticMethods);\n\n// Proxy to instance methods to constructor, for now, for backwards compatibility\nObject.keys(instanceMethods).forEach(key => {\n  /**\n   * @param {...any} args\n   * @returns {any | undefined}\n   */\n  SweetAlert[key] = function (...args) {\n    if (currentInstance && currentInstance[key]) {\n      return currentInstance[key](...args);\n    }\n    return null;\n  };\n});\nSweetAlert.DismissReason = DismissReason;\nSweetAlert.version = '11.22.2';\n\nconst Swal = SweetAlert;\n// @ts-ignore\nSwal.default = Swal;\n\n\n\"undefined\"!=typeof document&&function(e,t){var n=e.createElement(\"style\");if(e.getElementsByTagName(\"head\")[0].appendChild(n),n.styleSheet)n.styleSheet.disabled||(n.styleSheet.cssText=t);else try{n.innerHTML=t}catch(e){n.innerText=t}}(document,\":root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:\\\"top-start     top            top-end\\\" \\\"center-start  center         center-end\\\" \\\"bottom-start  bottom-center  bottom-end\\\";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:\\\"!\\\";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/sweetalert2/dist/sweetalert2.esm.all.js\n");

/***/ })

};
;