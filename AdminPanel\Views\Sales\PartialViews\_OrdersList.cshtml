@model Entities.MainModels.SalesModel


@await Html.PartialAsync("~/Views/Common/_DataTableLength.cshtml")

<table class="table site-table-listing">
    <thead>
        <tr>
            <th id="lbl_hdr_ordId"> 
                Order # 
                <div class="btn-group">
                    <a href="#" onclick="sortByOrderId('ASC')" class="btn btn-xs btn-outline-primary" title="Sort Ascending"><i class="icon-arrow-up12"></i></a>
                    <a href="#" onclick="sortByOrderId('DESC')" class="btn btn-xs btn-outline-primary" title="Sort Descending"><i class="icon-arrow-down12"></i></a>
                </div>
            </th>
            <th id="lbl_hdr_ordNo"> Order No</th>
            <th id="lbl_hdr_ordStatus"> Status</th>
            <th id="lbl_hdr_ordCustomer"> Customer</th>
            <th id="lbl_hdr_ordPoint"> Point</th>
            <th id="lbl_hdr_ordCoupon"> Coupon</th>
            <th id="lbl_hdr_ordCreatedOn"> Created On</th>
            <th id="lbl_hdr_ordTotal"> Order Total</th>
            <th id="lbl_hdr_ordFinalPrice"> Final Price</th>
            <th id="lbl_hdr_ordExchangeRate"> Exchange Rate</th>
            <th id="lbl_hdr_ordCurrencyCode"> Currency Code</th>



            <th class="text-center" style="width: 20px;"><i class="icon-arrow-down12"></i></th>
        </tr>
    </thead>
    <tbody>

        @{
            if (Model != null && Model.OrdersList != null && Model.OrdersList.Count > 0)
            {
                foreach (var item in Model.OrdersList)
                {
                    <tr>
                        <td><span class="text-dark">@item.OrderId</span></td>
                        <td><span class="text-dark">@item.OrderNumber</span></td>



                        @{
                            if (!String.IsNullOrWhiteSpace(item.LatestStatusName))
                            {
                                if (item.LatestStatusName == "Active")
                                {
                                    <td>
                                        <a href="#!" onclick="OpenOrderChangeStatusModel('@item.OrderId' , '@item.LatestStatusId')">
                                            <span class="badge bg-blue">@item.LatestStatusName</span>
                                        </a>

                                    </td>
                                }
                                else if (item.LatestStatusName == "In Progress")
                                {
                                    <td>

                                        <a href="#!" onclick="OpenOrderChangeStatusModel('@item.OrderId' , '@item.LatestStatusId')">
                                            <span class="badge badge-secondary">@item.LatestStatusName</span>
                                        </a>


                                    </td>
                                }
                                else if (item.LatestStatusName == "Completed")
                                {
                                    <td>


                                        <a href="#!" onclick="OpenOrderChangeStatusModel('@item.OrderId' , '@item.LatestStatusId')">
                                            <span class="badge bg-success-400">@item.LatestStatusName</span>
                                        </a>

                                    </td>
                                }

                                else if (item.LatestStatusName == "Returned")
                                {
                                    <td>

                                        <a href="#!" onclick="OpenOrderChangeStatusModel('@item.OrderId' , '@item.LatestStatusId')">
                                            <span class="badge badge-danger">@item.LatestStatusName</span>
                                        </a>

                                    </td>
                                }
                                else
                                {
                                    <td>


                                        <a href="#!" onclick="OpenOrderChangeStatusModel('@item.OrderId' , '@item.LatestStatusId')">
                                            <span class="badge bg-warning">@item.LatestStatusName</span>
                                        </a>

                                    </td>

                                }
                            }
                            else
                            {
                                  <td>


                                        <a href="#!" onclick="OpenOrderChangeStatusModel('@item.OrderId' , '@item.LatestStatusId')">
                                            <span class="badge bg-warning">@item.LatestStatusName</span>
                                        </a>

                                    </td>
                            }
                        }



                        <td>
                            <div class="d-flex align-items-center">
                                <div>
                                    <a href="https://wa.me/@(item.CustomerMobileNo)" class="text-default font-weight-semibold">@item.CustomerName</a>

                                </div>
                            </div>
                        </td>

                        <td><span class="text-dark">@(item.CustomerPointno ?? 0)</span></td>

                        <td>
                            @if (!string.IsNullOrWhiteSpace(item.CouponCode) && item.DiscountId.HasValue && item.DiscountId > 0)
                            {
                                <a href="@Url.Action("update-discount", "discounts", new { DiscountID = item.DiscountId })" class="badge badge-success">@item.CouponCode</a>
                            }
                            else if (!string.IsNullOrWhiteSpace(item.CouponCode))
                            {
                                <span class="badge badge-success">@item.CouponCode</span>
                            }
                            else
                            {
                                <span class="text-muted">-</span>
                            }
                        </td>

                        <td><span class="text-dark">@(item.OrderDateUtc.ToString("dd MMM, yyyy HH:mm"))</span></td>

                        <td>
                            <div class="d-flex align-items-center">
                                <div>
                                    <a href="#" class="text-default font-weight-semibold">@(CommonConversionHelper.GetDefaultCurrencySymbol())@item.OrderTotal</a>
                                </div>
                            </div>
                        </td>

                        <td>
                            <div class="d-flex align-items-center">
                                <div>
                                    <span class="text-default font-weight-semibold">@(CommonConversionHelper.GetDefaultCurrencySymbol())@(item.TotalFinal?.ToString("N2") ?? "0.00")</span>
                                </div>
                            </div>
                        </td>

                        <td>
                            <span class="text-dark">@(item.ExchangeRate.HasValue ? (item.ExchangeRate.Value % 1 == 0 ? item.ExchangeRate.Value.ToString("F0") : item.ExchangeRate.Value.ToString()) : "1")</span>
                        </td>

                        <td>
                            <span class="text-dark">@(item.CurrencyCode ?? "N/A")</span>
                        </td>

                        <td class="text-center">
                            <div class="">
                                <a href="@Url.Action("OrderDetail","Sales", new {langCode = Model?.PageBasicInfoObj?.langCode ,   OrderId=item.OrderId})" class="dropdown-item text-indigo-600"><i class="icon-eye"></i>View</a>
                            </div>
                        </td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td class="text-center" colspan="23"><b>  No record found </b></td>

                </tr>
            }
        }




    </tbody>
</table>



<div class="row" id="pagination_div">

    @{
        if (Model != null && Model.pageHelperObj != null)
        {
            PagerHelper ph = new PagerHelper();
            ph.CurrentPage = Model.pageHelperObj.CurrentPage;
            ph.TotalRecords = Model.pageHelperObj.TotalRecords;
            ph.RecordsPerPage = Model.pageHelperObj.RecordsPerPage;
            ph.EntityId = Model?.PageBasicInfoObj?.EntityId ?? 0;
            ph.PageUrl = Url.Action("OrdersList", "Sales", new { langCode = Model?.PageBasicInfoObj?.langCode });

            ph.AjaxEnabled = true;
            ph.OnClientClickAjaxCall = "PaginationAfterAjax(this)";

            @await Html.PartialAsync("~/Views/Common/_Pager.cshtml", ph)
        }

    }


</div>


