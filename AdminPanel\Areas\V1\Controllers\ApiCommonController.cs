using AdminPanel.Helpers.EmailSenderHelper;
using DAL.Repository.IServices;
using DAL.DBContext;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Spreadsheet;
using Entities.DBInheritedModels;
using Entities.DBModels;
using Helpers.ApiHelpers;
using Helpers.AuthorizationHelpers;
using Helpers.AuthorizationHelpers.JwtTokenHelper;
using Helpers.CommonHelpers;
using Helpers.CommonHelpers.Enums;
using Helpers.CommonHelpers.ICommonHelpers;
using Helpers.ConversionHelpers;
using Helpers.ConversionHelpers.IConversionHelpers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Primitives;
using Newtonsoft.Json;
using Stripe;
using Stripe.FinancialConnections;
using System.Diagnostics;
using System.Security.Policy;
using Dapper;

namespace AdminPanel.Areas.V1.Controllers
{
    [Route("api/v1/common")] //-- "common" is controller name with out api keyword"
    [ApiController]
    [Area("V1")]
    public class ApiCommonController : ControllerBase
    {

        private readonly IApiOperationServicesDAL _apiOperationServicesDAL;
        private readonly ICalculationHelper _calculationHelper;
        private readonly ICommonServicesDAL _commonServicesDAL;
        private readonly ISessionManager _sessionManag;
        private readonly IConstants _constants;
        private readonly IUserManagementServicesDAL _userManagementServicesDAL;
        private readonly IConfiguration _configuration;
        private readonly IEmailSender _emailSender;
        private readonly IOrderHelper _orderHelper;
        private readonly IProductServicesDAL _productServicesDAL;
        private readonly IBasicDataServicesDAL _basicDataServicesDAL;
        private readonly ISalesServicesDAL _salesServicesDAL;
        private readonly IFilesHelpers _filesHelpers;
        private readonly IDataContextHelper _contextHelper;

        public ApiCommonController(IApiOperationServicesDAL apiOperationServices, ICommonServicesDAL commonServicesDAL, ISessionManager sessionManag,
            IConstants constants, ICalculationHelper calculationHelper, IUserManagementServicesDAL userManagementServicesDAL, IConfiguration configuration,
            IEmailSender emailSender, IOrderHelper orderHelper, IProductServicesDAL productServicesDAL, IBasicDataServicesDAL basicDataServicesDAL, ISalesServicesDAL salesServicesDAL,
            IFilesHelpers filesHelpers, IDataContextHelper contextHelper)
        {
            this._apiOperationServicesDAL = apiOperationServices;
            this._commonServicesDAL = commonServicesDAL;
            this._sessionManag = sessionManag;
            this._constants = constants;
            this._calculationHelper = calculationHelper;
            this._userManagementServicesDAL = userManagementServicesDAL;
            this._configuration = configuration;
            this._emailSender = emailSender;
            this._orderHelper = orderHelper;
            this._productServicesDAL = productServicesDAL;
            this._basicDataServicesDAL = basicDataServicesDAL;
            this._salesServicesDAL = salesServicesDAL;
            this._filesHelpers = filesHelpers;
            this._contextHelper = contextHelper;
        }


        [Route("validate-email-send-otp/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> ValidateEmailAndSendOTP(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {


                if (param != null && param.Count != 0)
                {
                    Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }


                    string? Email = requestParameters != null ? requestParameters["Email"].ToString() : "";
                    if (String.IsNullOrEmpty(Email))
                    {

                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please fill email field!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }


                    //-- 1. Valiedate email from data base if exists
                    var user = await _userManagementServicesDAL.GetUserByEmailAddressDAL(Email);
                    if (user == null || user.UserId < 1)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Incorrect email. Please enter your correct email address!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    //-- 2. Generate OTP and save in database
                    int OTP = CommonConversionHelper.GenerateRandomNumber();
                    string OTPResponseFromDB = await this._userManagementServicesDAL.SaveOTPLogInformationDAL((short)ApiStatusCodes.OK, "Peding", "OTP Generated", OTP, null, Email, null, null, true, null);

                    if (String.IsNullOrEmpty(OTPResponseFromDB) || OTPResponseFromDB != "Saved Successfully!")
                    {
                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "An error occured in saving OTP. Please try again!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    //-- 3. Send OTP in email to user
                    try
                    {
                        List<EmailAddressEntity> emailAddresses = new List<EmailAddressEntity>();
                        emailAddresses.Add(new EmailAddressEntity { DisplayName = "User", Address = Email });
                        string SiteTitle = _configuration.GetSection("AppSetting").GetSection("WebsiteTitle").Value;
                        var message = new EmailMessage(emailAddresses, "Recover Password", String.Format("Your OTP for password recovery is: {0}", OTP), String.Format("{0} , Recover Password", SiteTitle));
                        _emailSender.SendEmail(message);
                    }
                    catch (Exception ex)
                    {
                        await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);

                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "An error occured in sending email. Please try again!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }


                    //-- 4. return user success and lets user enter otp that he recieve in email and password and confirm password
                    #region result

                    result.Data = "[]";
                    result.StatusCode = 200;
                    result.StatusMessage = "Ok";
                    result.Message = "Sent Successfully";
                    result.ErrorMessage = String.Empty;
                    apiActionResult = new APIActionResult(result);

                    #endregion


                }
                else
                {
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "An error is occured while processing your request.";
                    apiActionResult = new APIActionResult(result);
                }

            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }


            return apiActionResult;
        }


        [Route("validate-otp-change-password/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> ValidateOTPAndChangePassword(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {


                if (param != null && param.Count != 0)
                {
                    Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }


                    string? Email = requestParameters != null ? requestParameters["Email"].ToString() : "";
                    int? Otp = requestParameters != null ? Convert.ToInt32(requestParameters["Otp"].ToString()) : 0;
                    string? Password = requestParameters != null ? requestParameters["Password"].ToString() : "";
                    string? ConfirmPassword = requestParameters != null ? requestParameters["ConfirmPassword"].ToString() : "";



                    #region validation area

                    if (String.IsNullOrEmpty(Email))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please fill email field!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (Otp == null)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please fill OTP field!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (String.IsNullOrEmpty(Password))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please enter password!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (String.IsNullOrEmpty(ConfirmPassword))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please enter confirm password!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (Password.Length < 6 || ConfirmPassword.Length < 6)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Password & Confirm Password fields lenght should not be less than 6 characters!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    if (Password != ConfirmPassword)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Password does not match!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    #endregion


                    //-- 1. Valiedate email from data base if exists
                    var user = await _userManagementServicesDAL.GetUserByEmailAddressDAL(Email);
                    if (user == null || user.UserId < 1)
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Incorrect email. Please try again!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;

                    }

                    //-- 2. Validate the OTP from data base
                    var IsValidOTP = await this._userManagementServicesDAL.ValidateOTPByEmailDAL(Email, Convert.ToInt32(Otp));

                    //--Update the OTP Count by Email
                    string UpdateOTPResponse = await this._userManagementServicesDAL.UpdateOTPAttemptsByEmailDAL(Email);


                    if (IsValidOTP != null && !String.IsNullOrWhiteSpace(IsValidOTP.EmailAddress))
                    {

                        string PasswordResetResponse = "";
                        //-- 3. Reset user password
                        Password = CommonConversionHelper.Encrypt(Password);
                        PasswordResetResponse = await this._userManagementServicesDAL.ResetUserPasswordDAL(Email, Password);


                        //--De activate otps by email address
                        string DeActivateResponse = await this._userManagementServicesDAL.DeActivateOTPsByEmail(Email);



                        if (PasswordResetResponse == "Saved Successfully!")
                        {
                            #region result

                            result.Data = "[]";
                            result.StatusCode = 200;
                            result.StatusMessage = "Ok";
                            result.Message = "Password reset successfully";
                            result.ErrorMessage = String.Empty;
                            apiActionResult = new APIActionResult(result);

                            #endregion

                        }
                        else
                        {
                            result.StatusCode = 204;
                            result.StatusMessage = "Error";
                            result.ErrorMessage = "An error occured. Please try again";
                            apiActionResult = new APIActionResult(result);
                            return apiActionResult;
                        }


                    }
                    else
                    {

                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Invalid OTP that you enter!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }



                }
                else
                {
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "An error is occured while processing your request.";
                    apiActionResult = new APIActionResult(result);
                }

            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }


            return apiActionResult;
        }


        [Route("test-notifications")]
        [HttpGet]
        public async Task<APIActionResult> TestNotifications()
        {
            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            result.ActionType = ActionTypeEnum.JSON;

            try
            {
                using (var repo = _contextHelper.GetDataContextHelper())
                {
                    var notifications = repo.Fetch<dynamic>("SELECT TOP 5 * FROM AdminPanelNotifications ORDER BY CreatedOn DESC");
                    result.Data = notifications;
                    result.StatusCode = 200;
                    result.StatusMessage = "Ok";
                    result.ErrorMessage = string.Empty;
                }

                await Task.FromResult(result);
            }
            catch (Exception ex)
            {
                result.Data = null;
                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = ex.Message;
            }

            var apiActionResult = new APIActionResult(result);
            return apiActionResult;
        }
        [Route("post-order-direct")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> PostCustomerOrderDirect([FromBody] PostCustomerOrderRequest request)
        {
            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;
            result.ActionType = ActionTypeEnum.JSON;
            try
            {
                // Get user ID from JWT token (automatically extracted by middleware)
                int? authenticatedUserId = this.GetCurrentUserIdAsInt();
                // Use authenticated user ID if available, otherwise fall back to request parameter
                int effectiveUserId = authenticatedUserId ?? request.UserID;
                // Validate user ID
                if (effectiveUserId <= 0)
                    throw new ArgumentException("User authentication required - invalid or missing user ID");
                // If both token and request specify user ID, ensure they match for security
                if (authenticatedUserId.HasValue && request.UserID > 0 && authenticatedUserId.Value != request.UserID)
                    throw new UnauthorizedAccessException("Token user ID does not match request user ID");
                // Update request with authenticated user ID
                request.UserID = effectiveUserId;
                Console.WriteLine($"Order placement - Authenticated User ID: {authenticatedUserId}, Effective User ID: {effectiveUserId}");

                if (string.IsNullOrEmpty(request.cartJsonData))
                    throw new ArgumentException("Cart data is required");
                if (request.OrderTotal <= 0)
                    throw new ArgumentException("Order total must be greater than 0");

                // Parse cart data with enhanced debugging
                Console.WriteLine($"🔍 Raw cart JSON data: {request.cartJsonData}");
                var cartItems = JsonConvert.DeserializeObject<List<CustomerFinalOrderItemData>>(request.cartJsonData);
                Console.WriteLine($"[DEBUG] Received cartJsonData: {request.cartJsonData}");

                if (cartItems == null || !cartItems.Any())
                {
                    Console.WriteLine("❌ Cart items is null or empty after deserialization");
                    throw new ArgumentException("Cart cannot be empty");
                }

                Console.WriteLine($"✅ Successfully parsed {cartItems.Count} cart items");
                foreach (var item in cartItems)
                {
                    Console.WriteLine($"   - ProductId: {item.ProductId}, Price: {item.Price}, Quantity: {item.Quantity}, ItemPriceTotal: {item.ItemPriceTotal}");
                    Console.WriteLine($"     ShippingCharges: {item.ShippingChargesTotal}, AttributeCharges: {item.OrderItemAttributeChargesTotal}");
                    Console.WriteLine($"     ProductAllSelectedAttributes: {item.ProductAllSelectedAttributes ?? "null"}");
                    Console.WriteLine($"[DEBUG] CartItem - ProductId: {item.ProductId}, OrderItemAttributeChargesTotal: {item.OrderItemAttributeChargesTotal}");
                }

                // Get user's shipping address ID
                var userAddressId = request.addressid;

                using (var repo = _contextHelper.GetDataContextHelper())
                {
                    // --- START: Enhanced Coupon Code Handling ---
                    int? validatedCouponDiscountId = null;
                    int? couponDiscountTypeId = null;
                    decimal couponDiscountValue = 0;
                    int? couponDiscountValueType = null;
                    string? couponProductIds = null;
                    int? couponCategoryId = null;

                    if (!string.IsNullOrWhiteSpace(request.CouponCode))
                    {
                        Console.WriteLine($"Processing coupon code: {request.CouponCode}");

                        // Enhanced query to get all coupon details including discount type and value
                        var discountQuery = @"SELECT TOP 1
    d.DiscountID,
    d.Title,
    d.Description,
    d.DiscountTypeId,
    d.DiscountValueType,
    d.DiscountValue,
    d.StartDate,
    d.EndDate,
    d.CouponCode,
    d.MaxQuantity,
    d.IsBoundToMaxQuantity,
    d.IsCouponCodeRequired,
    d.IsActive,
    d.CreatedOn,
    d.CreatedBy,
    d.ModifiedOn,
    d.ModifiedBy,
    -- Get mapped ProductIds as comma-separated string
    STUFF((
        SELECT ',' + CAST(dpm.ProductID AS VARCHAR)
        FROM [codemedi_shop].[dbo].[DiscountProductsMapping] dpm
        WHERE dpm.DiscountID = d.DiscountID
        FOR XML PATH('')
    ), 1, 1, '') AS ProductIds
FROM [codemedi_shop].[dbo].[Discounts] d
WHERE d.CouponCode = @0
  AND d.IsActive = 1
  AND d.IsCouponCodeRequired = 1
  AND (@1 >= d.StartDate OR d.StartDate IS NULL)
  AND (@1 <= d.EndDate OR d.EndDate IS NULL);";

                        var discount = repo.Query<dynamic>(discountQuery, request.CouponCode.Trim(), DateTime.Now.Date).FirstOrDefault();

                        if (discount == null)
                        {
                            throw new ArgumentException($"Invalid, inactive, or expired coupon code: {request.CouponCode}");
                        }
                        else
                        {
                            // Check usage limits if the coupon is bound to a quantity
                            if ((bool)discount.IsBoundToMaxQuantity)
                            {
                                int maxQuantity = discount.MaxQuantity ?? int.MaxValue;
                                if (maxQuantity > 0)
                                {
                                    var usageCountQuery = "SELECT COUNT(*) FROM DiscountUsageHistory WHERE DiscountID = @0";
                                    int currentUsage = repo.ExecuteScalar<int>(usageCountQuery, (int)discount.DiscountID);

                                    if (currentUsage >= maxQuantity)
                                    {
                                        throw new ArgumentException($"Coupon code '{request.CouponCode}' has reached its maximum usage limit.");
                                    }
                                }
                            }

                            // Store coupon details for later processing
                            validatedCouponDiscountId = discount.DiscountID;
                            couponDiscountTypeId = discount.DiscountTypeId;
                            couponDiscountValue = Convert.ToDecimal(discount.DiscountValue ?? 0);
                            couponDiscountValueType = discount.DiscountValueType;
                            couponProductIds = discount.ProductIds?.ToString();


                            Console.WriteLine($"Coupon validated - ID: {validatedCouponDiscountId}, Type: {couponDiscountTypeId}, Value: {couponDiscountValue}, ValueType: {couponDiscountValueType}");

                            // Validate coupon applicability based on discount type
                            if (couponDiscountTypeId == 2 || couponDiscountTypeId == 3) // Applied on products
                            {
                                if (!string.IsNullOrEmpty(couponProductIds))
                                {
                                    var applicableProductIds = couponProductIds.Split(',').Select(id => int.TryParse(id.Trim(), out int productId) ? productId : 0).Where(id => id > 0).ToList();
                                    var cartProductIds = cartItems.Select(item => item.ProductId).ToList();

                                    if (!applicableProductIds.Any(id => cartProductIds.Contains(id)))
                                    {
                                        throw new ArgumentException($"Coupon code '{request.CouponCode}' is not applicable to any products in your cart.");
                                    }

                                    Console.WriteLine($"Coupon applicable to products: {string.Join(", ", applicableProductIds.Where(id => cartProductIds.Contains(id)))}");
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine("No coupon code provided.");
                    }
                    // --- END: Enhanced Coupon Code Handling ---

                    int orderId = 0;
                    string orderNumber = "";

                    // Begin transaction for data consistency using PetaPoco's transaction management
                    using (var transaction = repo.GetTransaction())
                    {
                        try
                        {
                            Console.WriteLine("=== STARTING ENHANCED ORDER CREATION PROCESS ===");

                            // Get exchange rate from AppConfigs with proper error handling and conversion
                            decimal exchangeRate = 1;
                            try
                            {
                                // First, get the raw value as string
                                var exchangeRateStr = repo.ExecuteScalar<string>(
                                    "SELECT AppConfigValue FROM AppConfigs WHERE AppConfigKey = 'currencyprice'");

                                if (!string.IsNullOrEmpty(exchangeRateStr))
                                {
                                    // Try to parse the value, handle different decimal separators
                                    if (decimal.TryParse(exchangeRateStr, System.Globalization.NumberStyles.Any,
                                        System.Globalization.CultureInfo.InvariantCulture, out decimal parsedRate))
                                    {
                                        exchangeRate = parsedRate;
                                        Console.WriteLine($"Exchange rate parsed successfully: {exchangeRate}");

                                        // If the rate is very large (like 1430), it's likely not the actual rate but a multiplier
                                        // So we might want to divide it by 100 or 1000 to get a reasonable exchange rate

                                    }
                                    else
                                    {
                                        Console.WriteLine($"Failed to parse exchange rate value: {exchangeRateStr}");
                                    }
                                }
                                else
                                {
                                    Console.WriteLine("No exchange rate found in AppConfigs, using default 1");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error getting exchange rate, using default 1: {ex.Message}");
                                exchangeRate = 1;
                            }

                            // Ensure exchange rate is within reasonable bounds
                            if (exchangeRate <= 0)
                            {
                                Console.WriteLine($"Invalid exchange rate {exchangeRate}, defaulting to 1");
                                exchangeRate = 1;
                            }

                            // Enhanced User Points Validation using exact SQL query as requested
                            decimal currentUserPoints = 0;
                            bool pointsColumnExists = true;
                            bool userExists = false;

                            try
                            {
                                // Use the exact SQL query provided by user for retrieving user points
                                var userPointsQuery = @"SELECT TOP 1
                            UserId,
                            UserTypeId,
                            FirstName,
                            MiddleName,
                            LastName,
                            UserName,
                            EmailAddress,
                            PhoneNo,
                            MobileNo,
                            IsVerified,
                            IsActive,
                            PointNo,
                            CreatedOn,
                            ModifiedOn
                        FROM Users
                        WHERE UserId = @0
                            AND IsActive = 1";

                                var userRecord = repo.Query<dynamic>(userPointsQuery, request.UserID).FirstOrDefault();

                                if (userRecord != null)
                                {
                                    userExists = true;
                                    currentUserPoints = Convert.ToDecimal(userRecord.PointNo ?? 0);
                                    Console.WriteLine($"User {request.UserID} found - Current points: {currentUserPoints}");
                                }
                                else
                                {
                                    throw new Exception($"User with ID {request.UserID} not found or is not active.");
                                }
                            }
                            catch (Exception ex) when (ex.Message.Contains("Invalid column name 'PointNo'"))
                            {
                                Console.WriteLine("PointNo column not found in Users table, defaulting to 0 points for user " + request.UserID);
                                currentUserPoints = 0;
                                pointsColumnExists = false;
                                userExists = true; // Assume user exists but points column doesn't
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error retrieving user points: {ex.Message}");
                                throw new Exception($"Failed to validate user points: {ex.Message}");
                            }

                            // Validate and process points usage
                            var pointsToUse = request.Point ?? 0;
                            if (pointsToUse > 0)
                            {
                                if (!userExists)
                                {
                                    throw new Exception($"Cannot use points - User {request.UserID} not found or inactive.");
                                }

                                if (!pointsColumnExists)
                                {
                                    Console.WriteLine("Cannot use points - PointNo column not found. Proceeding with order without point deduction.");
                                    // Reset points to use to 0 since we can't process them
                                    pointsToUse = 0;
                                    request.Point = null;
                                }
                                else if (currentUserPoints < pointsToUse)
                                {
                                    throw new Exception($"Insufficient points. User has {currentUserPoints} points but trying to use {pointsToUse} points.");
                                }
                                else
                                {
                                    // Deduct points from user's balance
                                    try
                                    {
                                        var newPointBalance = currentUserPoints - pointsToUse;
                                        repo.Execute("UPDATE Users SET PointNo = @0 WHERE UserId = @1", newPointBalance, request.UserID);
                                        Console.WriteLine($"Points deducted successfully. Previous balance: {currentUserPoints}, Used: {pointsToUse}, New balance: {newPointBalance}");
                                    }
                                    catch (Exception ex) when (ex.Message.Contains("Invalid column name 'PointNo'"))
                                    {
                                        Console.WriteLine("Cannot update points - PointNo column not found for user " + request.UserID);
                                        throw new Exception("Points system is not properly configured - PointNo column missing.");
                                    }
                                }
                            }
                            else
                            {
                                Console.WriteLine("No points being used for this order.");
                            }

                            // Determine and log order scenario for processing
                            bool hasPoints = pointsToUse > 0;
                            bool hasCoupon = validatedCouponDiscountId.HasValue;

                            string orderScenario = "";
                            if (!hasPoints && !hasCoupon)
                            {
                                orderScenario = "1 - Normal order without points and coupon";
                            }
                            else if (hasPoints && !hasCoupon)
                            {
                                orderScenario = "2 - Normal order with points only";
                            }
                            else if (!hasPoints && hasCoupon)
                            {
                                orderScenario = "3 - Normal order with coupon only";
                            }
                            else if (hasPoints && hasCoupon)
                            {
                                orderScenario = "4 - Normal order with both points and coupon";
                            }

                            Console.WriteLine($"=== ORDER SCENARIO: {orderScenario} ===");
                            Console.WriteLine($"Points to use: {pointsToUse}");
                            Console.WriteLine($"Coupon code: {request.CouponCode ?? "None"}");
                            Console.WriteLine($"Coupon discount ID: {validatedCouponDiscountId ?? 0}");
                            Console.WriteLine($"Coupon discount type: {couponDiscountTypeId ?? 0} (1=Order Total, 2=Products, 3=Products)");
                            Console.WriteLine($"Order total: {request.OrderTotal}");

                            // Get "Active" order status ID
                            var activeStatusId = repo.ExecuteScalar<int?>("SELECT TOP 1 StatusId FROM OrderStatuses WHERE StatusName = 'Active'") ?? 1;
                            Console.WriteLine($"Active status ID: {activeStatusId}");

                            // Enhanced Order insertion with proper DiscountId handling for order-level coupons
                            Console.WriteLine($"Inserting order with ExchangeRate: {exchangeRate}, OrderTotal: {request.OrderTotal}");

                            // Determine if coupon should be applied at order level (DiscountTypeId = 1)
                            int? orderLevelDiscountId = null;
                            if (validatedCouponDiscountId.HasValue && couponDiscountTypeId == 1)
                            {
                                orderLevelDiscountId = validatedCouponDiscountId;
                                Console.WriteLine($"Applying order-level discount: DiscountId = {orderLevelDiscountId}");
                            }

                            // Calculate order-level totals (points should only be applied at order level, not per item)
                            decimal orderTotalDiscountAmount = 0; // Initialize without points - points will be handled separately
                            if (validatedCouponDiscountId.HasValue && couponDiscountTypeId == 1)
                            {
                                // Add coupon discount amount for order-level coupons
                                var couponDiscountAmount = repo.ExecuteScalar<decimal?>("SELECT DiscountValue FROM Discounts WHERE DiscountID = @0", validatedCouponDiscountId.Value) ?? 0;
                                orderTotalDiscountAmount += couponDiscountAmount;
                            }

                            // Sum of all OrderItemAttributeCharges from cart items
                            decimal orderTotalAttributeCharges = cartItems.Sum(item => item.OrderItemAttributeChargesTotal);

                            // After processing all order items, we need to add the sum of all OrderItemDiscountTotal
                            // This should be calculated after the order items loop
                            decimal totalItemDiscounts = 0; // This will be calculated in the order items processing loop

                            // Update OrderTotalDiscountAmount to include item-level discounts
                            orderTotalDiscountAmount += totalItemDiscounts;

                            // Calculate TotalFinal according to your formula:
                            // TotalFinal = OrderTotal + OrderTotalAttributeCharges - OrderTotalDiscountAmount
                            decimal orderTotalFinal = request.OrderTotal + orderTotalAttributeCharges - orderTotalDiscountAmount;

                            // The calculatedOrderTotal should just be the OrderTotal for database storage
                            decimal calculatedOrderTotal = request.OrderTotal;

                            Console.WriteLine($"Order calculation - OrderTotal: {calculatedOrderTotal}, AttributeCharges: {orderTotalAttributeCharges}, DiscountAmount: {orderTotalDiscountAmount}, TotalFinal: {orderTotalFinal}");

                            orderId = repo.ExecuteScalar<int>(@"INSERT INTO Orders (CustomerId, OrderDateUtc, ShippingAddressId, OrderTotal, Point, LatestStatusId, ExchangeRate,
                                     OrderTotalDiscountAmount, OrderTotalShippingCharges, OrderTotalAttributeCharges, OrderTax, DiscountId, TotalFinal, CurrencyCode)
                                     OUTPUT INSERTED.OrderId
                                     VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10, @11, @12, @13)",
                                request.UserID, DateTime.Now, userAddressId, calculatedOrderTotal, request.Point ?? (object)DBNull.Value, activeStatusId, exchangeRate,
                                orderTotalDiscountAmount, 0, orderTotalAttributeCharges, 0, orderLevelDiscountId ?? (object)DBNull.Value, orderTotalFinal, request.CurrencyCode ?? (object)DBNull.Value);

                            // Verify the order was inserted with correct ExchangeRate
                            var insertedExchangeRate = repo.ExecuteScalar<decimal?>("SELECT ExchangeRate FROM Orders WHERE OrderId = @0", orderId);
                            Console.WriteLine($"Order inserted with ID: {orderId}, Verified ExchangeRate: {insertedExchangeRate}");

                            // Generate order number and update the order
                            orderNumber = $"OR#{orderId:00000000}";
                            repo.Execute("UPDATE Orders SET OrderNumber = @0 WHERE OrderId = @1", orderNumber, orderId);
                            Console.WriteLine($"Order number updated: {orderNumber}");

                            // Add order status mapping entry
                            repo.Execute(@"INSERT INTO OrderStatusesMapping (OrderId, StatusId, IsActive, CreatedOn, CreatedBy)
               VALUES (@0, @1, @2, @3, @4)",
                                orderId, activeStatusId, true, DateTime.Now, request.UserID);

                            // Add basic order note with essential details
                            try
                            {
                                Console.WriteLine($"Attempting to insert order note for OrderId: {orderId}");

                                // Create a basic note with essential order details
                                string orderNote = $"Order created. " +
                                                $"Order Total: {request.OrderTotal}. " +
                                                $"Customer ID: {request.UserID}. " +
                                                $"Shipping Address ID: {userAddressId}.";

                                // Add the custom order note if provided
                                if (!string.IsNullOrEmpty(request.OrderNote))
                                {
                                    orderNote += $"\n\nCustomer Note: {request.OrderNote}";
                                }

                                // Insert the basic order note
                                repo.Execute(@"INSERT INTO OrderNotes (OrderID, Message, CreatedBy, CreatedOn, ModifiedOn, ModifiedBy)
                  VALUES (@0, @1, @2, @3, @4, @5)",
                                    orderId, orderNote, request.UserID, DateTime.Now, DateTime.Now, request.UserID);

                                // Create a separate note for product attributes
                                string attributesNote = "=== ORDER ITEMS WITH ATTRIBUTES ===\n\n";
                                bool hasAttributes = false;

                                foreach (var cartItem in cartItems)
                                {
                                    // Get product name
                                    var productName = repo.ExecuteScalar<string>("SELECT ProductName FROM Products WHERE ProductID = @0", cartItem.ProductId) ?? "Unknown Product";
                                    attributesNote += $"PRODUCT: {productName}\n" +
                                                   $"Quantity: {cartItem.Quantity}\n" +
                                                   $"Unit Price: {cartItem.Price}\n" +
                                                   $"Subtotal: {cartItem.Price * cartItem.Quantity}\n";

                                    // Add product attributes if available
                                    if (!string.IsNullOrEmpty(cartItem.ProductAllSelectedAttributes))
                                    {
                                        try
                                        {
                                            var selectedAttributes = JsonConvert.DeserializeObject<List<dynamic>>(cartItem.ProductAllSelectedAttributes);
                                            if (selectedAttributes != null && selectedAttributes.Any())
                                            {
                                                hasAttributes = true;
                                                attributesNote += "\nPRODUCT ATTRIBUTES:\n";

                                                foreach (var attr in selectedAttributes)
                                                {
                                                    try
                                                    {
                                                        // FIXED: Use correct table and column names
                                                        var attributeName = repo.ExecuteScalar<string>(
                                                            "SELECT AttributeName FROM ProductAttributes WHERE ProductAttributeID = @0",
                                                            Convert.ToInt32(attr.ProductAttributeID ?? 0)) ?? "Unknown Attribute";

                                                        var valueName = string.IsNullOrEmpty(attr.AttributeValueText?.ToString())
                                                            ? repo.ExecuteScalar<string>(
                                                                "SELECT Name FROM ProductAttributeValues WHERE Id = @0",
                                                                Convert.ToInt32(attr.AttributeValueID ?? 0))
                                                            : attr.AttributeValueText;

                                                        var priceAdjustment = Convert.ToDecimal(attr.PriceAdjustment ?? 0);
                                                        var priceText = priceAdjustment != 0 ? $" (Price Adjustment: {priceAdjustment})" : "";

                                                        attributesNote += $"- {attributeName}: {valueName}{priceText}\n";
                                                    }
                                                    catch (Exception attrEx)
                                                    {
                                                        Console.WriteLine($"Error processing attribute for order note: {attrEx.Message}");
                                                        // Add a fallback entry so the note isn't completely empty
                                                        attributesNote += $"- Error retrieving attribute details (ID: {attr.ProductAttributeID ?? 0})\n";
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"Error processing product attributes for order note: {ex.Message}");
                                            attributesNote += "Error retrieving product attributes.\n";
                                        }
                                    }
                                    else
                                    {
                                        attributesNote += "\nNo attributes selected for this product.\n";
                                    }

                                    attributesNote += "\n" + new string('-', 50) + "\n\n";
                                }

                                // Always insert the attributes note, even if no attributes (shows all products)
                                repo.Execute(@"INSERT INTO OrderNotes (OrderID, Message, CreatedBy, CreatedOn, ModifiedOn, ModifiedBy)
                  VALUES (@0, @1, @2, @3, @4, @5)",
                                    orderId, attributesNote, request.UserID, DateTime.Now, DateTime.Now, request.UserID);

                                // Add exchange rate note as N/A
                                string exchangeRateNote = "EXCHANGE RATE: N/A\n" +
                                                      $"Order Total: {request.OrderTotal}\n" +
                                                      "No currency conversion applied.";

                                repo.Execute(@"INSERT INTO OrderNotes (OrderID, Message, CreatedBy, CreatedOn, ModifiedOn, ModifiedBy)
                  VALUES (@0, @1, @2, @3, @4, @5)",
                                    orderId, exchangeRateNote, request.UserID, DateTime.Now, DateTime.Now, request.UserID);

                                // Verify the notes were inserted
                                var noteCount = repo.ExecuteScalar<int>("SELECT COUNT(*) FROM OrderNotes WHERE OrderID = @0", orderId);
                                Console.WriteLine($"Order notes inserted successfully. Total notes for order: {noteCount}");

                                // Update OrderShippingDetail with N/A for exchange rate
                                try
                                {

                                    Console.WriteLine($"OrderShippingDetail updated with N/A exchange rate for OrderID: {orderId}");
                                }
                                catch (Exception shippingDetailEx)
                                {
                                    Console.WriteLine($"Error updating OrderShippingDetail with exchange rate: {shippingDetailEx.Message}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error inserting order note: {ex.Message}");
                                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                            }

                            Console.WriteLine($"Order ID: {orderId}");
                            Console.WriteLine("Processing order items");
                            decimal calculatedTotal = 0;
                            totalItemDiscounts = 0; // Reset for actual calculation

                            // Process each cart item with enhanced error handling
                            Console.WriteLine($"📦 Starting to process {cartItems.Count} cart items for Order ID: {orderId}");
                            int processedItems = 0;
                            int failedItems = 0;

                            foreach (var cartItem in cartItems)
                            {
                                try
                                {
                                    Console.WriteLine($"🔄 Processing cart item {processedItems + 1}/{cartItems.Count} - ProductId: {cartItem.ProductId}, Quantity: {cartItem.Quantity}, Price: {cartItem.Price}");

                                    // Calculate Order Item Total = (Price * Quantity) - Discount
                                    // NOTE: Points are handled at ORDER level only, not per item to avoid double deduction

                                    // Use ItemSubTotal if non-zero, otherwise calculate without points (points handled at order level)
                                    decimal itemTotal = cartItem.ItemSubTotal != 0 ? cartItem.ItemSubTotal :
                                        ((cartItem.Price * cartItem.Quantity) - (cartItem.OrderItemDiscountTotal ?? 0));

                                    Console.WriteLine($"Item {cartItem.ProductId}: Price={cartItem.Price}, Qty={cartItem.Quantity}, Discount={cartItem.OrderItemDiscountTotal ?? 0}, Total={itemTotal} (Points handled at order level)");

                                    // Generate OrderItemGuid for this order item
                                    var orderItemGuid = Guid.NewGuid();
                                    int orderItemId = 0;

                                    // Get vendor commission ID
                                    int? vendorId = repo.ExecuteScalar<int?>("SELECT TOP 1 VendorID FROM Products WHERE ProductID = @0", cartItem.ProductId);
                                    int? vendorCommissionId = null;

                                    if (vendorId.HasValue)
                                    {
                                        vendorCommissionId = repo.ExecuteScalar<int?>(
                                            @"SELECT TOP 1 VendorCommissionID 
                      FROM VendorsCommissionSetup 
                      WHERE UserID = @0 AND IsActive = 1 
                      AND (CAST(GETDATE() AS DATE) BETWEEN CAST(ApplicableFrom AS DATE) AND CAST(ApplicableTo AS DATE))",
                                            vendorId.Value);
                                        Console.WriteLine($"Vendor commission found for ProductID {cartItem.ProductId}: VendorCommissionID={vendorCommissionId}");
                                    }

                                    Console.WriteLine($"Processing product {cartItem.ProductId} with price {cartItem.Price} and attribute charges {cartItem.OrderItemAttributeChargesTotal}");

                                    // Enhanced OrderItem insertion with proper product-level coupon handling
                                    try
                                    {
                                        // Handle OrderItemAttributeChargesTotal with proper null checking and calculation
                                        decimal itemAttributeCharges = cartItem.OrderItemAttributeChargesTotal;

                                        // Always recalculate attribute charges from product attributes to ensure accuracy
                                        if (!string.IsNullOrEmpty(cartItem.ProductAllSelectedAttributes))
                                        {
                                            Console.WriteLine($"[DEBUG] Calculating OrderItemAttributeChargesTotal from attributes for Product {cartItem.ProductId}");

                                            try
                                            {
                                                var selectedAttributes = JsonConvert.DeserializeObject<List<dynamic>>(cartItem.ProductAllSelectedAttributes);
                                                decimal additionalCharges = 0;

                                                if (selectedAttributes != null)
                                                {
                                                    foreach (var attr in selectedAttributes)
                                                    {
                                                        try
                                                        {
                                                            var productAttributeId = Convert.ToInt32(attr.ProductAttributeID ?? 0);
                                                            var additionalPrice = Convert.ToDecimal(attr.PriceAdjustment ?? 0);

                                                            if (additionalPrice > 0)
                                                            {
                                                                additionalCharges += additionalPrice;
                                                                Console.WriteLine($"[DEBUG] Attribute {productAttributeId} adds {additionalPrice} to charges");
                                                            }
                                                        }
                                                        catch (Exception attrEx)
                                                        {
                                                            Console.WriteLine($"[DEBUG] Error processing attribute: {attrEx.Message}");
                                                        }
                                                    }
                                                }

                                                // Calculate total attribute charges for this item (per unit * quantity)
                                                itemAttributeCharges = additionalCharges * cartItem.Quantity;
                                                Console.WriteLine($"[DEBUG] Calculated OrderItemAttributeChargesTotal: {itemAttributeCharges} (per unit: {additionalCharges}, quantity: {cartItem.Quantity})");
                                            }
                                            catch (Exception ex)
                                            {
                                                Console.WriteLine($"[DEBUG] Error calculating attribute charges: {ex.Message}");
                                                // Keep the original value if calculation fails
                                            }
                                        }
                                        else
                                        {
                                            Console.WriteLine($"[DEBUG] No attributes found for Product {cartItem.ProductId}, using provided value: {itemAttributeCharges}");
                                        }

                                        object attributeChargesParam = itemAttributeCharges;

                                        Console.WriteLine($"💰 OrderItem Attribute Charges for Product {cartItem.ProductId}: {itemAttributeCharges}");

                                        // Handle ShippingChargesTotal with proper null checking
                                        decimal itemShippingCharges = cartItem.ShippingChargesTotal;

                                        // Calculate OrderItemDiscountTotal (excludes points, only coupon discounts)
                        decimal orderItemDiscountTotal = cartItem.OrderItemDiscountTotal ?? 0m;

                                        // Determine discount ID for this order item
                                        int? orderItemDiscountId = null;

                                        // First priority: item-specific discount ID
                                        if (cartItem.DiscountId.HasValue && cartItem.DiscountId > 0)
                                        {
                                            orderItemDiscountId = cartItem.DiscountId;
                                            Console.WriteLine($"Using item-specific discount ID {orderItemDiscountId} for product {cartItem.ProductId}");
                                        }
                                        // Second priority: product-level coupon (DiscountTypeId = 2 OR 3)
                                        else if (validatedCouponDiscountId.HasValue && (couponDiscountTypeId == 2 || couponDiscountTypeId == 3))
                                        {
                                            // Check if this product is eligible for the coupon
                                            bool isProductEligible = false;

                                            if (!string.IsNullOrEmpty(couponProductIds))
                                            {
                                                var applicableProductIds = couponProductIds.Split(',')
                                                    .Select(id => int.TryParse(id.Trim(), out int productId) ? productId : 0)
                                                    .Where(id => id > 0)
                                                    .ToList();

                                                isProductEligible = applicableProductIds.Contains(cartItem.ProductId);

                                                Console.WriteLine($"Checking product {cartItem.ProductId} eligibility for coupon:");
                                                Console.WriteLine($"  - Applicable Product IDs: {string.Join(", ", applicableProductIds)}");
                                                Console.WriteLine($"  - Current Product ID: {cartItem.ProductId}");
                                                Console.WriteLine($"  - Is Eligible: {isProductEligible}");
                                            }
                                            else
                                            {
                                                // If no specific products defined, apply to all products
                                                isProductEligible = true;
                                                Console.WriteLine($"No specific products defined for coupon, applying to all products including {cartItem.ProductId}");
                                            }

                                            if (isProductEligible)
                                            {
                                                orderItemDiscountId = validatedCouponDiscountId;
                                                // Add coupon discount amount to the item discount total
                                                var couponDiscountAmount = repo.ExecuteScalar<decimal?>("SELECT DiscountValue FROM Discounts WHERE DiscountID = @0", validatedCouponDiscountId.Value) ?? 0;
                                                orderItemDiscountTotal += couponDiscountAmount;
                                                Console.WriteLine($"✅ Applying product-level coupon discount ID {orderItemDiscountId} to product {cartItem.ProductId}, discount amount: {couponDiscountAmount}");
                                            }
                                            else
                                            {
                                                Console.WriteLine($"❌ Product {cartItem.ProductId} is NOT eligible for coupon {validatedCouponDiscountId}");
                                            }
                                        }

                                        // Calculate OrderItemTotal = (Price * Quantity) + AttributeCharges - DiscountTotal
                                        decimal calculatedOrderItemTotal = (cartItem.Price * cartItem.Quantity) + itemAttributeCharges - orderItemDiscountTotal;
                                        
                                        // Use calculated total or provided ItemSubTotal if available
                                        decimal finalOrderItemTotal = cartItem.ItemSubTotal != 0 ? cartItem.ItemSubTotal : calculatedOrderItemTotal;
                                        
                                        // Calculate TotalFinal for order item (final price after all additions and deductions)
                                        decimal orderItemTotalFinal = finalOrderItemTotal;

                                        Console.WriteLine($"🔄 Inserting OrderItem - ProductId: {cartItem.ProductId}, Price: {cartItem.Price}, Qty: {cartItem.Quantity}");
                                        Console.WriteLine($"   AttributeCharges: {itemAttributeCharges}, DiscountTotal: {orderItemDiscountTotal}, ItemTotal: {finalOrderItemTotal}, TotalFinal: {orderItemTotalFinal}");
                                        Console.WriteLine($"   OrderId: {orderId}, DiscountId: {orderItemDiscountId ?? 0}");

                                        try
                                        {
                                            // First, let's check if the OrderItems table structure is correct
                                            Console.WriteLine($"🔍 Checking OrderItems table structure...");
                                            var tableInfo = repo.Query<dynamic>("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'OrderItems' AND COLUMN_NAME IN ('ProductId', 'ProductID')");
                                            foreach (var col in tableInfo)
                                            {
                                                Console.WriteLine($"   Found column: {col.COLUMN_NAME}");
                                            }

                                            Console.WriteLine($"🔄 Executing OrderItem INSERT with parameters:");
                                            Console.WriteLine($"   @0 (OrderId): {orderId}");
                                            Console.WriteLine($"   @1 (ProductId): {cartItem.ProductId}");
                                            Console.WriteLine($"   @2 (Quantity): {cartItem.Quantity}");
                                            Console.WriteLine($"   @3 (Price): {cartItem.Price}");
                                            Console.WriteLine($"   @4 (ItemPriceTotal): {cartItem.ItemPriceTotal}");
                                            Console.WriteLine($"   @8 (DiscountId): {orderItemDiscountId ?? 0}");

                                            // Insert OrderItem with calculated totals
                                            orderItemId = repo.ExecuteScalar<int>(@"INSERT INTO OrderItems (OrderId, ProductId, Quantity, Price, ItemPriceTotal,
                                     OrderItemDiscountTotal, OrderItemShippingChargesTotal, OrderItemAttributeChargesTotal,
                                     DiscountId, OrderItemTotal, OrderItemGuid, OrderItemTaxTotal, TotalFinal)
                                     OUTPUT INSERTED.OrderItemID
                                     VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9, @10, @11, @12)",
                                  orderId, cartItem.ProductId, cartItem.Quantity, cartItem.Price, cartItem.ItemPriceTotal,
                                  orderItemDiscountTotal, itemShippingCharges, itemAttributeCharges,
                                  orderItemDiscountId ?? (object)DBNull.Value,
                                  finalOrderItemTotal, orderItemGuid, 0, orderItemTotalFinal);

                                            Console.WriteLine($"✅ OrderItem created successfully - ID: {orderItemId}, ProductId: {cartItem.ProductId}, DiscountId: {orderItemDiscountId ?? 0}, AttributeCharges: {itemAttributeCharges}");

                                            // Verify the discount was inserted correctly
                                            if (orderItemDiscountId.HasValue)
                                            {
                                                var insertedDiscountId = repo.ExecuteScalar<int?>("SELECT DiscountId FROM OrderItems WHERE OrderItemID = @0", orderItemId);
                                                Console.WriteLine($"🔍 Verification: OrderItem {orderItemId} has DiscountId = {insertedDiscountId}");
                                            }
                                            
                                            // Verify the attribute charges were inserted correctly
                                            var verifyQuery = "SELECT OrderItemAttributeChargesTotal FROM OrderItems WHERE OrderItemID = @0";
                                            var insertedValue = repo.ExecuteScalar<decimal?>(verifyQuery, orderItemId);
                                            Console.WriteLine($"[DEBUG] Verified OrderItemAttributeChargesTotal in DB: {insertedValue}");
                                        }
                                        catch (Exception insertEx)
                                        {
                                            Console.WriteLine($"❌ FAILED to insert OrderItem: {insertEx.Message}");
                                            Console.WriteLine($"   SQL Parameters: OrderId={orderId}, ProductId={cartItem.ProductId}, Quantity={cartItem.Quantity}");
                                            Console.WriteLine($"   DiscountId={orderItemDiscountId ?? 0}");
                                            Console.WriteLine($"   Inner Exception: {insertEx.InnerException?.Message}");
                                            Console.WriteLine($"   Stack trace: {insertEx.StackTrace}");
                                            throw; // Re-throw to see the full error
                                        }

                                        calculatedTotal += finalOrderItemTotal;
                                        totalItemDiscounts += orderItemDiscountTotal;

                                        // Update product stock quantity
                                        int newStockQuantity = repo.ExecuteScalar<int>("SELECT ISNULL(StockQuantity, 0) - @0 FROM Products WHERE ProductID = @1",
                                            cartItem.Quantity, cartItem.ProductId);
                                        repo.Execute("UPDATE Products SET StockQuantity = @0 WHERE ProductID = @1",
                                            newStockQuantity, cartItem.ProductId);
                                        Console.WriteLine($"Product stock updated for ProductID {cartItem.ProductId}: New quantity = {newStockQuantity}");

                                        // Enhanced discount usage history tracking
                                        if (orderItemDiscountId.HasValue && orderItemDiscountId > 0)
                                        {
                                            try
                                            {
                                                repo.Execute(@"INSERT INTO DiscountUsageHistory(DiscountID, UsedBy, UsageDate)
                                                 VALUES (@0, @1, @2)",
                                                    orderItemDiscountId.Value, request.UserID, DateTime.Now);
                                                Console.WriteLine($"✅ Discount usage recorded for DiscountID {orderItemDiscountId.Value} (OrderItem ID: {orderItemId}, Product ID: {cartItem.ProductId})");
                                            }
                                            catch (Exception ex)
                                            {
                                                Console.WriteLine($"❌ Error recording discount usage history: {ex.Message}");
                                                // Don't throw - this is not critical for order completion
                                            }
                                        }
                                        else
                                        {
                                            Console.WriteLine($"ℹ️ No discount applied to OrderItem {orderItemId}, Product {cartItem.ProductId}");
                                        }

                                        // FIXED: Insert OrderProductAttributeMapping records for product attributes/options
                                        try
                                        {
                                            if (!string.IsNullOrEmpty(cartItem.ProductAllSelectedAttributes))
                                            {
                                                var selectedAttributes = JsonConvert.DeserializeObject<List<dynamic>>(cartItem.ProductAllSelectedAttributes);
                                                if (selectedAttributes != null && selectedAttributes.Any())
                                                {
                                                    Console.WriteLine($"Processing {selectedAttributes.Count} attributes for OrderItem {orderItemId}");
                                                    foreach (var attribute in selectedAttributes)
                                                    {
                                                        try
                                                        {
                                                            var productAttributeId = Convert.ToInt32(attribute.ProductAttributeID ?? 0);
                                                            var attributeValueId = Convert.ToInt32(attribute.AttributeValueID ?? 0);
                                                            var additionalPrice = Convert.ToDecimal(attribute.PriceAdjustment ?? 0);
                                                            var attributeValueText = attribute.AttributeValueText?.ToString() ?? "";

                                                            if (productAttributeId > 0 && attributeValueId > 0)
                                                            {
                                                                repo.Execute(@"INSERT INTO OrderProductAttributeMapping (ProductAttributeID, OrderItemID, AttributeValue, AttrAdditionalPrice)
                                              VALUES (@0, @1, @2, @3)",
                                                                    productAttributeId, orderItemId, attributeValueId, additionalPrice);
                                                                Console.WriteLine($"OrderProductAttributeMapping created: ProductAttributeID={productAttributeId}, AttributeValueID={attributeValueId}, Price={additionalPrice}");
                                                            }
                                                            else
                                                            {
                                                                Console.WriteLine($"Skipping attribute with invalid IDs: ProductAttributeID={productAttributeId}, AttributeValueID={attributeValueId}");
                                                            }
                                                        }
                                                        catch (Exception attrEx)
                                                        {
                                                            Console.WriteLine($"Error processing individual attribute: {attrEx.Message}");
                                                        }
                                                    }
                                                }
                                                else
                                                {
                                                    Console.WriteLine($"No valid attributes found for OrderItem {orderItemId}");
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"Error creating OrderProductAttributeMapping: {ex.Message}");
                                        }

                                        // ADDED: Create order note for product attributes after insertion
                                        try
                                        {
                                            if (!string.IsNullOrEmpty(cartItem.ProductAllSelectedAttributes))
                                            {
                                                var selectedAttributes = JsonConvert.DeserializeObject<List<dynamic>>(cartItem.ProductAllSelectedAttributes);
                                                if (selectedAttributes != null && selectedAttributes.Any())
                                                {
                                                    // Get product name for the note
                                                    var productName = repo.ExecuteScalar<string>("SELECT ProductName FROM Products WHERE ProductID = @0", cartItem.ProductId) ?? "Unknown Product";

                                                    // Build attribute details for the note
                                                    string attributeNote = $"PRODUCT ATTRIBUTES ADDED\n\n";
                                                    attributeNote += $"Product: {productName} (ID: {cartItem.ProductId})\n";
                                                    attributeNote += $"Order Item ID: {orderItemId}\n";
                                                    attributeNote += $"Quantity: {cartItem.Quantity}\n\n";
                                                    attributeNote += "Selected Attributes:\n";

                                                    foreach (var attribute in selectedAttributes)
                                                    {
                                                        try
                                                        {
                                                            var productAttributeId = Convert.ToInt32(attribute.ProductAttributeID ?? 0);
                                                            var attributeValueId = Convert.ToInt32(attribute.AttributeValueID ?? 0);
                                                            var additionalPrice = Convert.ToDecimal(attribute.PriceAdjustment ?? 0);
                                                            var attributeValueText = attribute.AttributeValueText?.ToString() ?? "";

                                                            // Get attribute name from database - FIXED table name
                                                            var attributeName = repo.ExecuteScalar<string>(
                                                                "SELECT AttributeName FROM ProductAttributes WHERE ProductAttributeID = @0",
                                                                productAttributeId) ?? "Unknown Attribute";

                                                            var priceText = additionalPrice != 0 ? $" (+${additionalPrice})" : "";
                                                            attributeNote += $"- {attributeName}: {attributeValueText}{priceText}\n";
                                                        }
                                                        catch (Exception noteEx)
                                                        {
                                                            Console.WriteLine($"Error building attribute note: {noteEx.Message}");
                                                            attributeNote += "- Error retrieving attribute details\n";
                                                        }
                                                    }

                                                    attributeNote += $"\nTotal attributes mapped: {selectedAttributes.Count}\n";
                                                    attributeNote += $"Created on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                                                    // Insert the order note
                                                    repo.Execute(@"INSERT INTO OrderNotes (OrderID, Message, CreatedBy, CreatedOn)
                                      VALUES (@0, @1, @2, @3)",
                                                        orderId, attributeNote, request.UserID, DateTime.Now);

                                                    Console.WriteLine($"Product attributes order note created for OrderItem {orderItemId}");
                                                }
                                            }
                                        }
                                        catch (Exception noteEx)
                                        {
                                            Console.WriteLine($"Error creating product attributes order note: {noteEx.Message}");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"Error in product attribute note creation: {ex.Message}");
                                    }
                                    processedItems++;
                                    Console.WriteLine($"✅ Successfully processed cart item {processedItems}/{cartItems.Count}");
                                }
                                catch (Exception orderItemEx)
                                {
                                    failedItems++;
                                    Console.WriteLine($"❌ CRITICAL ERROR: Failed to insert OrderItem for ProductId {cartItem.ProductId}: {orderItemEx.Message}");
                                    Console.WriteLine($"   Cart item {processedItems + 1}/{cartItems.Count} failed");
                                    Console.WriteLine($"   Stack trace: {orderItemEx.StackTrace}");
                                    // Don't throw - continue with other items, but log the error
                                    // This ensures OrderShippingDetail can still be created for successful items
                                }
                            }

                            Console.WriteLine($"📊 Cart processing summary: {processedItems} successful, {failedItems} failed out of {cartItems.Count} total items");

                            // Now update the order totals with the actual calculated values from database
                            // Get the ACTUAL calculated totals from the OrderItems that were just inserted
                            var actualOrderTotalsQuery = repo.Query<dynamic>(@"SELECT
        SUM(ISNULL(OrderItemShippingChargesTotal,0)) AS ShippingCharges,
        SUM(ISNULL(OrderItemTaxTotal,0)) AS Tax,
        SUM(ISNULL(OrderItemDiscountTotal,0)) AS ItemDiscounts,
        SUM(ISNULL(OrderItemAttributeChargesTotal,0)) AS AttributeCharges,
        SUM(ISNULL(OrderItemTotal,0)) AS Total
        FROM OrderItems WHERE OrderID = @0", orderId).FirstOrDefault();

                            decimal actualShippingCharges = 0, actualTax = 0, actualItemDiscounts = 0, actualAttributeCharges = 0, actualTotal = 0;

                            if (actualOrderTotalsQuery != null)
                            {
                                var totalsDict = actualOrderTotalsQuery as IDictionary<string, object>;
                                if (totalsDict != null)
                                {
                                    actualShippingCharges = Convert.ToDecimal(totalsDict["ShippingCharges"] ?? 0);
                                    actualTax = Convert.ToDecimal(totalsDict["Tax"] ?? 0);
                                    actualItemDiscounts = Convert.ToDecimal(totalsDict["ItemDiscounts"] ?? 0);
                                    actualAttributeCharges = Convert.ToDecimal(totalsDict["AttributeCharges"] ?? 0);
                                    actualTotal = Convert.ToDecimal(totalsDict["Total"] ?? 0);
                                }
                            }

                            // Calculate order-level discount amount (excluding points and item-level discounts)
                            decimal orderLevelDiscountAmount = 0;
                            if (validatedCouponDiscountId.HasValue && couponDiscountTypeId == 1)
                            {
                                var couponDiscountAmount = repo.ExecuteScalar<decimal?>("SELECT DiscountValue FROM Discounts WHERE DiscountID = @0", validatedCouponDiscountId.Value) ?? 0;
                                orderLevelDiscountAmount += couponDiscountAmount;
                            }

                            // Total discount amount = order-level discounts + item-level discounts (but NOT points)
                            decimal totalDiscountAmount = orderLevelDiscountAmount + actualItemDiscounts;

                            // Recalculate final total using ACTUAL values: OrderTotal + AttributeCharges - DiscountAmount - Points
                            // Points are handled separately from discounts to avoid double deduction
                            orderTotalFinal = request.OrderTotal + actualAttributeCharges - totalDiscountAmount - pointsToUse;

                            Console.WriteLine($"Final order calculation - OrderTotal: {request.OrderTotal}, AttributeCharges: {actualAttributeCharges}, TotalDiscountAmount: {totalDiscountAmount}, Points: {pointsToUse}, TotalFinal: {orderTotalFinal}");
                            Console.WriteLine($"Breakdown - Item-level discounts: {actualItemDiscounts}, Order-level discounts: {orderLevelDiscountAmount}, Total discounts: {totalDiscountAmount}");

                            // Get the latest exchange rate and order details
                            try
                            {
                                var orderDetails = repo.Query<dynamic>(
                                    "SELECT ExchangeRate, OrderTotal, OrderNumber FROM Orders WHERE OrderID = @0",
                                    orderId).FirstOrDefault();

                                decimal orderExchangeRate = orderDetails?.ExchangeRate ?? 1m;
                                decimal orderTotal = orderDetails?.OrderTotal ?? 0m;
                                string exchangeRateOrderNumber = orderDetails?.OrderNumber ?? string.Empty;

                                // Add exchange rate note to order notes
                                if (orderExchangeRate != 1m) // Only add note if exchange rate is not 1
                                {
                                    try
                                    {
                                        string exchangeNote = $"Exchange Rate Applied: {orderExchangeRate}. " +
                                                          $"Order Total (after exchange): {orderTotal}. " +
                                                          $"Order Number: {exchangeRateOrderNumber}";

                                        repo.Execute(@"INSERT INTO OrderNotes (OrderID, Message, CreatedBy, CreatedOn)
                      VALUES (@0, @1, @2, @3)",
                                            orderId, exchangeNote, request.UserID, DateTime.Now);

                                        Console.WriteLine($"Exchange rate note added to order {orderId}. Rate: {orderExchangeRate}");
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"Error adding exchange rate note: {ex.Message}");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error retrieving order exchange rate details: {ex.Message}");
                            }

                            // Update Orders table with the ACTUAL calculated totals (already calculated above)
                            Console.WriteLine($"[DEBUG] Updating Orders table with - AttributeCharges: {actualAttributeCharges}, TotalDiscountAmount: {totalDiscountAmount}, TotalFinal: {orderTotalFinal}");

                            // Update Orders table with calculated totals
                            // Note: OrderTotalDiscountAmount should only include coupon/discount amounts, NOT points
                            repo.Execute(@"UPDATE Orders SET
OrderNumber = @0,
OrderTotalShippingCharges = @1,
OrderTax = @2,
OrderTotalDiscountAmount = @3,
OrderTotalAttributeCharges = @4,
OrderTotal = @5,
TotalFinal = @7
WHERE OrderID = @6",
                                orderNumber, actualShippingCharges, actualTax, totalDiscountAmount, actualAttributeCharges, actualTotal, orderId, orderTotalFinal);

                            Console.WriteLine($"Order totals updated: Shipping={actualShippingCharges}, Tax={actualTax}, " +
                                             $"DiscountAmount={totalDiscountAmount}, Attributes={actualAttributeCharges}, Total={actualTotal}, TotalFinal={orderTotalFinal}");

                            // Add verification after UPDATE
                            var verifyOrderQuery = "SELECT OrderTotalAttributeCharges, OrderTotalDiscountAmount FROM Orders WHERE OrderId = @0";
                            var updatedOrder = repo.Query<dynamic>(verifyOrderQuery, orderId).FirstOrDefault();
                            Console.WriteLine($"[DEBUG] Verified Orders table - OrderTotalAttributeCharges: {updatedOrder?.OrderTotalAttributeCharges}, OrderTotalDiscountAmount: {updatedOrder?.OrderTotalDiscountAmount}");
                            
                            // Verify the update was successful
                            var verifyOrder = repo.Query<dynamic>("SELECT OrderTotalDiscountAmount, OrderTotalAttributeCharges, TotalFinal, OrderTotal, Point FROM Orders WHERE OrderID = @0", orderId).FirstOrDefault();
                            if (verifyOrder != null)
                            {
                                Console.WriteLine($"✅ Order {orderId} final verification:");
                                Console.WriteLine($"   OrderTotal: {verifyOrder.OrderTotal}");
                                Console.WriteLine($"   AttributeCharges: {verifyOrder.OrderTotalAttributeCharges}");
                                Console.WriteLine($"   DiscountAmount: {verifyOrder.OrderTotalDiscountAmount}");
                                Console.WriteLine($"   Points: {verifyOrder.Point ?? 0}");
                                Console.WriteLine($"   TotalFinal: {verifyOrder.TotalFinal}");
                                Console.WriteLine($"   Expected TotalFinal: {request.OrderTotal} + {verifyOrder.OrderTotalAttributeCharges} - {verifyOrder.OrderTotalDiscountAmount} - {verifyOrder.Point ?? 0} = {request.OrderTotal + Convert.ToDecimal(verifyOrder.OrderTotalAttributeCharges ?? 0) - Convert.ToDecimal(verifyOrder.OrderTotalDiscountAmount ?? 0) - Convert.ToDecimal(verifyOrder.Point ?? 0)}");
                            }

                            // Record order-level coupon usage history (DiscountTypeId = 1)
                            if (validatedCouponDiscountId.HasValue && couponDiscountTypeId == 1)
                            {
                                try
                                {
                                    repo.Execute(@"INSERT INTO DiscountUsageHistory(DiscountID, UsedBy, UsageDate)
                                 VALUES (@0, @1, @2)",
                                        validatedCouponDiscountId.Value, request.UserID, DateTime.Now);
                                    Console.WriteLine($"Order-level coupon usage recorded for DiscountID {validatedCouponDiscountId.Value} (Order ID: {orderId})");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error recording order-level coupon usage history: {ex.Message}");
                                    // Don't throw - this is not critical for order completion
                                }
                            }

                            // FIXED: Insert OrderShippingDetail records for all order items
                            // First, fetch all order item IDs into a list to avoid DataReader issues
                            var orderItemIds = repo.Fetch<int>("SELECT OrderItemID FROM OrderItems WITH (NOLOCK) WHERE OrderID = @0", orderId).ToList();
                            Console.WriteLine($"Found {orderItemIds.Count} order items to create shipping details for");

                            // Now process each order item
                            foreach (var orderShippingItemId in orderItemIds)
                            {
                                try
                                {
                                    Console.WriteLine($"Inserting OrderShippingDetail for OrderItem {orderShippingItemId}, OrderID {orderId}, StatusID {activeStatusId}");
                                    repo.Execute(@"INSERT INTO OrderShippingDetail (OrderID, OrderItemID, ShippingStatusID)
                  VALUES (@0, @1, @2)",
                                        orderId, orderShippingItemId, activeStatusId);
                                    Console.WriteLine($"OrderShippingDetail created successfully for OrderItem {orderShippingItemId}");
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"Error creating OrderShippingDetail for OrderItem {orderShippingItemId}: {ex.Message}");
                                    Console.WriteLine($"Stack trace: {ex.StackTrace}");
                                    // Continue with other items even if one fails
                                }
                            }

                            // Verify shipping details were created
                            var shippingDetailCount = repo.ExecuteScalar<int>("SELECT COUNT(*) FROM OrderShippingDetail WHERE OrderID = @0", orderId);
                            Console.WriteLine($"Total OrderShippingDetail records created: {shippingDetailCount}");

                            // Digital Products handling
                            int cashOnDeliveryMethod = repo.ExecuteScalar<int?>(
                                "SELECT TOP 1 PaymentMethodID FROM PaymentMethods WHERE PaymentMethodName = 'Cash on delivery'") ?? 6;

                            if (request.PaymentMethod != cashOnDeliveryMethod)
                            {
                                // Update shipping status for digital products to "Completed"
                                int completedStatusId = repo.ExecuteScalar<int?>(
                                    "SELECT TOP 1 StatusID FROM OrderStatuses WHERE StatusName = 'Completed'") ?? 2;

                                repo.Execute(@"UPDATE OrderShippingDetail 
               SET ShippingStatusID = @0
               WHERE OrderItemId IN (
                   SELECT oi.OrderItemId 
                   FROM OrderItems oi 
                   INNER JOIN Products p ON oi.ProductId = p.ProductId 
                   WHERE oi.OrderID = @1 AND p.IsDigitalProduct = 1
               )", completedStatusId, orderId);
                                Console.WriteLine($"Digital product shipping status updated to Completed");

                                // Check if all products are digital
                                int totalDigitalProducts = repo.ExecuteScalar<int>(
                                    @"SELECT COUNT(*) 
      FROM OrderItems oi
      INNER JOIN Products p ON oi.ProductId = p.ProductId
      WHERE oi.OrderID = @0 AND p.IsDigitalProduct = 1", orderId);

                                int totalOrderProducts = repo.ExecuteScalar<int>(
                                    @"SELECT COUNT(*) 
      FROM OrderItems oi
      INNER JOIN Products p ON oi.ProductId = p.ProductId
      WHERE oi.OrderID = @0", orderId);

                                if (totalDigitalProducts > 0 && totalDigitalProducts == totalOrderProducts)
                                {
                                    // All products are digital, mark order as completed
                                    repo.Execute(@"INSERT INTO OrderStatusesMapping(OrderID, StatusID, IsActive, CreatedOn, CreatedBy)
                   VALUES (@0, @1, 1, @2, @3)",
                                        orderId, completedStatusId, DateTime.Now, request.UserID);

                                    repo.Execute("UPDATE Orders SET LatestStatusID = @0 WHERE OrderID = @1",
                                        completedStatusId, orderId);
                                    Console.WriteLine($"All products are digital, order status updated to Completed");
                                }
                            }

                            // Get CurrencyID from CurrencyCode
                            int currencyId = 1; // Default to 1 if not found
                            if (!string.IsNullOrEmpty(request.CurrencyCode))
                            {
                                currencyId = repo.ExecuteScalar<int?>(
                                    "SELECT TOP 1 CurrencyID FROM Currencies WHERE CurrencyCode = @0", request.CurrencyCode) ?? 1;
                                Console.WriteLine($"Currency found: Code={request.CurrencyCode}, ID={currencyId}");
                            }

                            // Create payment record based on payment method (updated to match stored procedure logic)
                            string milestoneName = "Milestone 1";
                            bool isCompleted = true;

                            // Get payment method IDs
                            int? stripePaymentMethod = repo.ExecuteScalar<int?>(
                                "SELECT TOP 1 PaymentMethodID FROM PaymentMethods WHERE PaymentMethodName = 'Stripe'");
                            int? payPalPaymentMethod = repo.ExecuteScalar<int?>(
                                "SELECT TOP 1 PaymentMethodID FROM PaymentMethods WHERE PaymentMethodName = 'PayPal'");

                            // Get final order total for payment
                            var finalOrderTotal = repo.ExecuteScalar<decimal>("SELECT TOP 1 OrderTotal FROM Orders WHERE OrderID = @0", orderId);

                            if (request.PaymentMethod == stripePaymentMethod)
                            {
                                repo.Execute(@"INSERT INTO OrdersPayments (OrderId, PaymentMethodId, MilestoneValue, MilestoneName,
             CurrencyId, IsCompleted, PaymentDate, StripeResponseJson, StripeChargeId,
             StripeBalanceTransactionId)
             VALUES (@0, @1, @2, @3, @4, @5, @6, @7, @8, @9)",
                                    orderId, request.PaymentMethod, finalOrderTotal, milestoneName, currencyId, isCompleted, DateTime.Now,
                                    request.StripeResponseJson ?? (object)DBNull.Value, request.StripeChargeId ?? (object)DBNull.Value,
                                    request.StripeBalanceTransactionId ?? (object)DBNull.Value);
                            }
                            else if (request.PaymentMethod == payPalPaymentMethod)
                            {
                                repo.Execute(@"INSERT INTO OrdersPayments (OrderId, PaymentMethodId, MilestoneValue, MilestoneName,
             CurrencyId, IsCompleted, PaymentDate, PayPalResponseJson)
             VALUES (@0, @1, @2, @3, @4, @5, @6, @7)",
                                    orderId, request.PaymentMethod, finalOrderTotal, milestoneName, currencyId, isCompleted, DateTime.Now,
                                    request.PayPalResponseJson ?? (object)DBNull.Value);
                            }
                            else
                            {
                                repo.Execute(@"INSERT INTO OrdersPayments (OrderId, PaymentMethodId, MilestoneValue, MilestoneName,
             CurrencyId, IsCompleted, PaymentDate)
             VALUES (@0, @1, @2, @3, @4, @5, @6)",
                                    orderId, request.PaymentMethod, finalOrderTotal, milestoneName, currencyId, isCompleted, DateTime.Now);
                            }
                            Console.WriteLine($"Payment record created for order {orderId} with CurrencyID={currencyId}");

                            // Insert admin panel notification for new order (using stored procedure call if available)
                            Console.WriteLine("Creating notification");
                            try
                            {
                                var firstName = repo.ExecuteScalar<string>("SELECT TOP 1 FirstName FROM Users WHERE UserID = @0", request.UserID) ?? "Customer";
                                var notificationMessage = $"New order {orderNumber} has been placed by {firstName} at {DateTime.Now.Date:yyyy-MM-dd}";

                                // Try to use the stored procedure first (matching the original SP behavior)
                                try
                                {
                                    repo.Execute("EXEC [dbo].[SP_InsertAdminPanelNotification] @0, @1, @2, @3",
                                        "New Order Placed", notificationMessage, 1, "");
                                    Console.WriteLine("Notification created using stored procedure");
                                }
                                catch
                                {
                                    // Fallback to direct insert if stored procedure doesn't exist
                                    var notificationId = repo.ExecuteScalar<int>(@"INSERT INTO AdminPanelNotifications (Title, Message, NotificationTypeId, IsRead, ClickUrl, CreatedOn)
                                  OUTPUT INSERTED.NotificationId
                                  VALUES (@0, @1, @2, @3, @4, @5)",
                                        "New Order Placed", notificationMessage, 1, false, "", DateTime.Now);
                                    Console.WriteLine($"Notification created with ID: {notificationId}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"Error creating notification: {ex.Message}");
                            }

                            // Commit transaction
                            transaction.Complete();
                            Console.WriteLine("Order creation completed successfully");

                            result.Data = new
                            {
                                OrderID = orderId,
                                OrderNumber = orderNumber,
                                Message = "Order Placed Successfully"
                            };
                            result.StatusCode = 200;
                            result.StatusMessage = "Ok";
                            result.ErrorMessage = string.Empty;
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error in order creation: {ex.Message}");
                            // Transaction will be automatically rolled back when disposed
                            throw;
                        }
                    }
                    await Task.FromResult(result);
                }
            }
            catch (Exception ex)
            {
                result.Data = null;
                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = ex.Message;
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
            }
            apiActionResult = new APIActionResult(result);
            return apiActionResult;
        }

        [Route("post-order/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> PostCustomerOrder(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";
            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;
            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);
            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion
            try
            {
                StripeConfiguration.ApiKey = _constants.GetAppSettingKeyValue("AppSetting", "StripeSecretKey");
                // strip implementation url
                // https://stripe.com/docs/payments/accept-a-payment-charges?html-or-react=react
                var paymentToken = "";
                int PaymentMethod = 0;
                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);
                            paymentToken = requestParameters["paymentToken"].ToString(); // Using ASP.NET MVC 
                            PaymentMethod = Convert.ToInt32(requestParameters["PaymentMethod"].ToString());
                        }
                    }
                }
                //--strip testing card urls: https://stripe.com/docs/testing?numbers-or-method-or-token=card-numbers#visa
                #region new
                string CouponCode = requestParameters["CouponCode"].ToString() ?? "";
                string? Description = "Order of customer id: " + requestParameters["UserID"].ToString() + " at " + DateTime.Now.ToString();
                string cartJsonData = "[]";
                decimal? OrderTotal = 0m;
                decimal? ItemSubTotal = 0;
                cartJsonData = requestParameters != null ? requestParameters["cartJsonData"].ToString() ?? "[]" : "[]";
                var cartCustomerProducts = new List<CartCustomerProducts>();
                cartCustomerProducts = JsonConvert.DeserializeObject<List<CartCustomerProducts>>(cartJsonData);
                if (cartCustomerProducts != null)
                {
                    List<ProductsIds>? ProductIds = new List<ProductsIds>();
                    foreach (var item in cartCustomerProducts)
                    {
                        var rowData = new ProductsIds();
                        rowData.ProductId = item.ProductId;
                        ProductIds.Add(rowData);
                    }
                    string ProductIdsJson = JsonConvert.SerializeObject(ProductIds);
                    //-- get products list by ids
                    Dictionary<string, object>? requestParametersAllProducts = new Dictionary<string, object>();
                    requestParametersAllProducts.Add("ProductsIds", ProductIdsJson);
                    var ApiConfigurationForGetAllProducts = await this._apiOperationServicesDAL.GetAPIConfiguration("get-products-list-by-ids");
                    string? allProductsDataJson = "[]";
                    if (ApiConfigurationForGetAllProducts != null)
                    {
                        allProductsDataJson = await this._apiOperationServicesDAL.GetApiData(requestParametersAllProducts, ApiConfigurationForGetAllProducts);
                    }
                    //--Calcualte Discount for products
                    string productsAfterDiscount = await _calculationHelper.CalculateDiscountsForProducts((allProductsDataJson ?? "[]"));
                    var CartItems = JsonConvert.DeserializeObject<List<ApiProductEntity?>>(productsAfterDiscount ?? "[]");
                    List<CustomerFinalOrderItemData> customerFinalOrderItemDataList = new List<CustomerFinalOrderItemData>();
                    if (CartItems != null)
                    {
                        if (CartItems.Any(x => x.DiscountedPrice > 0))
                        {
                            foreach (var item in CartItems)
                            {
                                //--get product attributes by product id
                                var ApiConfigForProductAttributes = await this._apiOperationServicesDAL.GetAPIConfiguration("get-product-all-attributes-by-productId");
                                var requestParametersAllAttributes = new Dictionary<string, object>();
                                requestParametersAllAttributes.Add("ProductID", item?.ProductId ?? 0);
                                string? productAllAttributesJson = await this._apiOperationServicesDAL.GetApiData(requestParametersAllAttributes, ApiConfigForProductAttributes);
                                var _cartProductAllAttributes = JsonConvert.DeserializeObject<List<CartProductAllAttributes?>>(productAllAttributesJson ?? "[]");
                                var _productSelectedAttributes = cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item?.ProductId)?.productSelectedAttributes;
                                item.Price = item.Price;
                                item.Quantity = Convert.ToInt32(cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item.ProductId)?.Quantity);
                                item.ShippingCharges = item.ShippingCharges * item.Quantity;
                                decimal additionalAttributesCharges = 0;
                                if (_productSelectedAttributes != null)
                                {
                                    for (int index = 0; index < _productSelectedAttributes.Count(); index++)
                                    {
                                        var priceData = _cartProductAllAttributes?.Where(x => x.ProductAttributeID == _productSelectedAttributes[index].ProductAttributeID
                                                        && x.PrimaryKeyValue == _productSelectedAttributes[index].PrimaryKeyValue)?.FirstOrDefault();
                                        additionalAttributesCharges = Convert.ToDecimal(additionalAttributesCharges + priceData?.AdditionalPrice);
                                    }
                                }
                                additionalAttributesCharges = additionalAttributesCharges * item.Quantity;
                                if (item.DiscountId > 0 && item.DiscountedPrice != null && item.DiscountedPrice > 0)
                                {
                                    item.DiscountedPrice = item.DiscountedPrice;
                                    item.OrderItemDiscount = item.Price - item.DiscountedPrice;
                                    item.OrderItemDiscount = item.OrderItemDiscount * item.Quantity;
                                }
                                item.ItemSubTotal = Convert.ToDecimal((item.DiscountedPrice > 0 ? item.DiscountedPrice : item.Price) * (item.Quantity));
                                item.ItemSubTotal = item.ItemSubTotal + (item.ShippingCharges ?? 0);
                                item.ItemSubTotal = item.ItemSubTotal + (additionalAttributesCharges);
                                OrderTotal = Convert.ToDecimal(OrderTotal + (item.ItemSubTotal));
                                //--Set All Selected attributes data for this row
                                item.ProductAllSelectedAttributes = new List<CartProductAllAttributes>();
                                if (_productSelectedAttributes != null)
                                {
                                    foreach (var attr in _productSelectedAttributes)
                                    {
                                        var fullDataAttribue = _cartProductAllAttributes.Where(x => x.ProductAttributeID == attr.ProductAttributeID && x.PrimaryKeyValue == attr.PrimaryKeyValue).FirstOrDefault();
                                        item.ProductAllSelectedAttributes.Add(fullDataAttribue);
                                    }
                                }
                                //--Fill final order item
                                CustomerFinalOrderItemData customerFinalOrderItemData = new CustomerFinalOrderItemData()
                                {
                                    ProductId = item.ProductId,
                                    Quantity = item.Quantity,
                                    Price = item.Price,
                                    ItemPriceTotal = item.Price * item.Quantity,
                                    ItemSubTotal = item.ItemSubTotal ?? 0,
                                    IsShippingFree = item.IsShippingFree ?? true,
                                    ShippingChargesTotal = item.ShippingCharges ?? 0,
                                    OrderItemAttributeChargesTotal = additionalAttributesCharges,
                                    DiscountId = item.DiscountId ?? 0,
                                    DiscountedPrice = item.DiscountedPrice ?? 0,
                                    OrderItemDiscountTotal = item.OrderItemDiscount ?? 0,
                                    IsDiscountCalculated = item.IsDiscountCalculated ?? false,
                                    CouponCode = item.CouponCode ?? "",
                                    ProductAllSelectedAttributes = item.ProductAllSelectedAttributes != null ?
                                        JsonConvert.SerializeObject(item.ProductAllSelectedAttributes) : "[]"
                                };
                                customerFinalOrderItemDataList.Add(customerFinalOrderItemData);
                            }
                        }
                        else if (!String.IsNullOrWhiteSpace(CouponCode))
                        {
                            bool IsDiscountExecuted = false;
                            foreach (var item in CartItems)
                            {
                                //--get product attributes by product id
                                var ApiConfigForProductAttributes = await this._apiOperationServicesDAL.GetAPIConfiguration("get-product-all-attributes-by-productId");
                                var requestParametersAllAttributes = new Dictionary<string, object>();
                                requestParametersAllAttributes.Add("ProductID", item?.ProductId ?? 0);
                                string? productAllAttributesJson = await this._apiOperationServicesDAL.GetApiData(requestParametersAllAttributes, ApiConfigForProductAttributes);
                                var _cartProductAllAttributes = JsonConvert.DeserializeObject<List<CartProductAllAttributes?>>(productAllAttributesJson ?? "[]");
                                var _productSelectedAttributes = cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item?.ProductId)?.productSelectedAttributes;
                                item.Price = item.Price;
                                item.Quantity = Convert.ToInt32(cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item.ProductId)?.Quantity);
                                item.ShippingCharges = item.ShippingCharges * item.Quantity;
                                decimal additionalAttributesCharges = 0;
                                if (_productSelectedAttributes != null)
                                {
                                    for (int index = 0; index < _productSelectedAttributes.Count(); index++)
                                    {
                                        var priceData = _cartProductAllAttributes?.Where(x => x.ProductAttributeID == _productSelectedAttributes[index].ProductAttributeID
                                                        && x.PrimaryKeyValue == _productSelectedAttributes[index].PrimaryKeyValue)?.FirstOrDefault();
                                        additionalAttributesCharges = Convert.ToDecimal(additionalAttributesCharges + priceData?.AdditionalPrice);
                                    }
                                }
                                additionalAttributesCharges = additionalAttributesCharges * item.Quantity;
                                //--Set All Selected attributes data for this row
                                item.ProductAllSelectedAttributes = new List<CartProductAllAttributes>();
                                if (_productSelectedAttributes != null)
                                {
                                    foreach (var attr in _productSelectedAttributes)
                                    {
                                        var fullDataAttribue = _cartProductAllAttributes.Where(x => x.ProductAttributeID == attr.ProductAttributeID && x.PrimaryKeyValue == attr.PrimaryKeyValue).FirstOrDefault();
                                        item.ProductAllSelectedAttributes.Add(fullDataAttribue);
                                    }
                                }
                                //--Fill final order item
                                CustomerFinalOrderItemData customerFinalOrderItemData = new CustomerFinalOrderItemData()
                                {
                                    ProductId = item.ProductId,
                                    Quantity = item.Quantity,
                                    Price = item.Price,
                                    ItemPriceTotal = item.Price * item.Quantity,
                                    IsShippingFree = item.IsShippingFree ?? true,
                                    ShippingChargesTotal = item.ShippingCharges ?? 0,
                                    OrderItemAttributeChargesTotal = additionalAttributesCharges,
                                    CouponCode = item.CouponCode ?? "",
                                    ProductAllSelectedAttributes = item.ProductAllSelectedAttributes != null ?
                                        JsonConvert.SerializeObject(item.ProductAllSelectedAttributes) : "[]"
                                };
                                //--If discount is applied from the coupon to a product then do not execute again for each product
                                if (item.IsDiscountAllowed == true && IsDiscountExecuted == false)
                                {
                                    var couponDiscount = await _calculationHelper.CalculateCouponDiscountValueForProduct(item.ProductId, item.Price, CouponCode, cartJsonData);
                                    if (couponDiscount != null)
                                    {
                                        decimal DiscountValueAfterCouponApplied = Convert.ToDecimal(couponDiscount["DiscountValueAfterCouponApplied"].ToString());
                                        customerFinalOrderItemData.DiscountId = Convert.ToInt32(couponDiscount["DiscountId"].ToString());
                                        customerFinalOrderItemData.CouponCode = CouponCode;
                                        item.OrderItemDiscount = DiscountValueAfterCouponApplied;
                                        item.DiscountedPrice = (item.Price - (DiscountValueAfterCouponApplied < item.Price ? DiscountValueAfterCouponApplied : 0));
                                        customerFinalOrderItemData.DiscountedPrice = item.DiscountedPrice ?? 0;
                                        customerFinalOrderItemData.IsDiscountCalculated = true;
                                        if (Convert.ToInt32(couponDiscount["DiscountTypeId"].ToString()) == (short)DiscountTypesEnum.AppliedOnOrderTotal)
                                        {
                                            customerFinalOrderItemData.OrderItemDiscountTotal = ((item.OrderItemDiscount ?? 0));
                                        }
                                        else
                                        {
                                            customerFinalOrderItemData.OrderItemDiscountTotal = ((item.OrderItemDiscount ?? 0) * item.Quantity);
                                        }
                                        //--set the flag to true
                                        IsDiscountExecuted = DiscountValueAfterCouponApplied > 0 ? true : false;
                                    }
                                }
                                item.ItemSubTotal = Convert.ToDecimal((item.DiscountedPrice > 0 ? item.DiscountedPrice : item.Price) * (item.Quantity));
                                item.ItemSubTotal = item.ItemSubTotal + (item.ShippingCharges ?? 0);
                                item.ItemSubTotal = item.ItemSubTotal + (additionalAttributesCharges);
                                customerFinalOrderItemData.ItemSubTotal = Convert.ToDecimal(item.ItemSubTotal);
                                OrderTotal = Convert.ToDecimal(OrderTotal + (item.ItemSubTotal));
                                customerFinalOrderItemDataList.Add(customerFinalOrderItemData);
                            }
                        }
                        if (OrderTotal == null || OrderTotal == 0 || OrderTotal < 0)
                        {
                            throw new InvalidOperationException("Invalid order total amount!");
                        }
                        //--Get Api Configuration
                        var ApiConfiguration = await this._apiOperationServicesDAL.GetAPIConfiguration(UrlName);

                        // FIXED: Changed .Add() to indexer syntax to prevent duplicate key exception
                        requestParameters["CurrencyCode"] = CommonConversionHelper.GetDefaultCurrencyCode()?.ToLower() ?? "usd";
                        requestParameters["OrderTotal"] = OrderTotal;
                        requestParameters["cartJsonData"] = JsonConvert.SerializeObject(customerFinalOrderItemDataList);

                        if (PaymentMethod == (short)PaymentMethodsEnum.Stripe)
                        {
                            if (String.IsNullOrWhiteSpace(paymentToken))
                            {
                                throw new InvalidOperationException("stripe payment token is empty!");
                            }
                            string currency = CommonConversionHelper.GetDefaultCurrencyCode()?.ToLower() ?? "usd";
                            var options = new ChargeCreateOptions
                            {
                                Amount = currency == "usd" ? (long)(OrderTotal * 100) : (long)OrderTotal,
                                Currency = currency,
                                Description = Description,
                                Source = paymentToken,
                            };
                            var service = new ChargeService();
                            var charge = service.Create(options);
                            if (charge.Status == "succeeded")
                            {
                                // FIXED: Changed .Add() to indexer syntax to prevent duplicate key exception
                                requestParameters["Description"] = Description;
                                requestParameters["StripeStatus"] = charge.Status;
                                requestParameters["StripeResponseJson"] = charge.StripeResponse.Content;
                                requestParameters["StripeBalanceTransactionId"] = charge.BalanceTransactionId;
                                requestParameters["StripeChargeId"] = charge.Id;
                                requestParameters["PayPalResponseJson"] = string.Empty;
                                // Add Point parameter if provided
                                if (requestParameters.ContainsKey("Point") && requestParameters["Point"] != null)
                                {
                                    // Point parameter is already in requestParameters, no need to add it again
                                }
                                else
                                {
                                    requestParameters["Point"] = null;
                                }
                                //--save the information in data base
                                data = await _orderHelper.SaveCustomerOrderInDbWithRetry(requestParameters, ApiConfiguration);
                                #region result
                                result.Data = data;
                                result.StatusCode = 200;
                                result.StatusMessage = "Ok";
                                result.ErrorMessage = String.Empty;
                                apiActionResult = new APIActionResult(result);
                                #endregion
                            }
                            else
                            {
                                #region result
                                result.Data = "[]";
                                result.StatusCode = 501;
                                result.StatusMessage = "Error";
                                result.ErrorMessage = "An error occured. Please try again";
                                apiActionResult = new APIActionResult(result);
                                #endregion
                            }
                        }
                        else if (PaymentMethod == (short)PaymentMethodsEnum.CashOnDelivery)
                        {
                            // FIXED: Changed .Add() to indexer syntax to prevent duplicate key exception
                            requestParameters["Description"] = Description;
                            requestParameters["StripeStatus"] = "";
                            requestParameters["StripeResponseJson"] = "";
                            requestParameters["StripeBalanceTransactionId"] = "";
                            requestParameters["StripeChargeId"] = "";
                            requestParameters["PayPalResponseJson"] = string.Empty;
                            // Add Point parameter if provided
                            if (requestParameters.ContainsKey("Point") && requestParameters["Point"] != null)
                            {
                                // Point parameter is already in requestParameters, no need to add it again
                            }
                            else
                            {
                                requestParameters["Point"] = null;
                            }
                            //--save the information in data base
                            data = await _orderHelper.SaveCustomerOrderInDbWithRetry(requestParameters, ApiConfiguration);
                            #region result
                            result.Data = data;
                            result.StatusCode = 200;
                            result.StatusMessage = "Ok";
                            result.ErrorMessage = String.Empty;
                            apiActionResult = new APIActionResult(result);
                            #endregion
                        }
                        else if (PaymentMethod == (short)PaymentMethodsEnum.PayPal)
                        {
                            // FIXED: Changed .Add() to indexer syntax to prevent duplicate key exception
                            requestParameters["Description"] = Description;
                            requestParameters["StripeStatus"] = "";
                            requestParameters["StripeResponseJson"] = "";
                            requestParameters["StripeBalanceTransactionId"] = "";
                            requestParameters["StripeChargeId"] = "";
                            requestParameters["PayPalResponseJson"] = requestParameters["payPalOrderConfirmJson"].ToString() ?? "";
                            // Add Point parameter if provided
                            if (requestParameters.ContainsKey("Point") && requestParameters["Point"] != null)
                            {
                                // Point parameter is already in requestParameters, no need to add it again
                            }
                            else
                            {
                                requestParameters["Point"] = null;
                            }
                            //--save the information in data base
                            data = await _orderHelper.SaveCustomerOrderInDbWithRetry(requestParameters, ApiConfiguration);
                            #region result
                            result.Data = data;
                            result.StatusCode = 200;
                            result.StatusMessage = "Ok";
                            result.ErrorMessage = String.Empty;
                            apiActionResult = new APIActionResult(result);
                            #endregion
                        }
                        else
                        {
                            #region result
                            result.Data = "[]";
                            result.StatusCode = 501;
                            result.StatusMessage = "Error";
                            result.ErrorMessage = "No payment method specified";
                            apiActionResult = new APIActionResult(result);
                            #endregion
                        }
                        #region Send email to customer if order placed successfully
                        try
                        {
                            if (!string.IsNullOrEmpty(Request.Headers["UserID"]) && result.StatusCode == 200)
                            {
                                int UserID = Convert.ToInt32(Request.Headers["UserID"].ToString());
                                var userData = _basicDataServicesDAL.GetUserDataByUserID(UserID);
                                List<EmailAddressEntity> emailAddresses = new List<EmailAddressEntity>();
                                string content = String.Format("Your order has been placed successfully. Order total amount is: {0} {1}. {2}Please check your order history page for further details. {2}{2} Thanks", (CommonConversionHelper.GetDefaultCurrencyCode()?.ToLower() ?? "USD"), OrderTotal, Environment.NewLine);
                                emailAddresses.Add(new EmailAddressEntity { DisplayName = "User", Address = userData?.EmailAddress });
                                string SiteTitle = _configuration.GetSection("AppSetting").GetSection("WebsiteTitle").Value;
                                var message = new EmailMessage(emailAddresses, "New Order Placed", content, String.Format("{0} , New Order Placed", SiteTitle));
                                _emailSender.SendEmail(message);
                            }
                        }
                        catch (Exception ex)
                        {
                            //-- Do nothing
                            var noThing = ex.Message;
                        }
                        #endregion
                    }
                    else
                    {
                        #region result
                        result.Data = "[]";
                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "No cart itme selected!";
                        apiActionResult = new APIActionResult(result);
                        #endregion
                    }
                }
                else
                {
                    #region result
                    result.Data = "[]";
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "No cart itme selected!";
                    apiActionResult = new APIActionResult(result);
                    #endregion
                }
                #endregion
            }
            catch (Exception ex)
            {
                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion
                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }
            return apiActionResult;
        }

        [Route("get-strp-pub-key/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetStripePublishableKey(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                //-- 4. return user success and lets user enter otp that he recieve in email and password and confirm password
                #region result
                string StripePublishableKey = _constants.GetAppSettingKeyValue("AppSetting", "StripePublishableKey");
                Dictionary<string, string> StripeDic = new Dictionary<string, string>();
                StripeDic.Add("strpK", "StripePublishableKey");

                result.Data = JsonConvert.SerializeObject(StripeDic);
                result.StatusCode = 200;
                result.StatusMessage = "Ok";
                result.Message = "Sent Successfully";
                result.ErrorMessage = String.Empty;
                apiActionResult = new APIActionResult(result);

                #endregion

            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }


            return apiActionResult;
        }

        [Route("get-customer-cart-items/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetCustomerLatestCartItems(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {

                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);



                        }

                    }

                }


                #region calcualtion
                //-- get customer cart data
                string cartJsonData = "[]";
                cartJsonData = requestParameters != null ? requestParameters["cartJsonData"].ToString() ?? "[]" : "[]";
                var apiResponse = await _calculationHelper.CalcualteProductsTotalAndAdditionalPrices(cartJsonData);
                #endregion







                #region result
                result.Data = JsonConvert.SerializeObject(apiResponse);
                result.StatusCode = 200;
                result.StatusMessage = "Ok";
                result.ErrorMessage = String.Empty;
                apiActionResult = new APIActionResult(result);
                #endregion


            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }





            return apiActionResult;


        }

        [Route("get-coupon-code-discount-value/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetCouponCodeDiscountedValue(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {

                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }

                }


                #region new
                string CouponCode = requestParameters["CouponCode"].ToString() ?? "";
                string cartJsonData = "[]";
                cartJsonData = requestParameters != null ? requestParameters["cartJsonData"].ToString() ?? "[]" : "[]";


                var cartCustomerProducts = new List<CartCustomerProducts>();
                cartCustomerProducts = JsonConvert.DeserializeObject<List<CartCustomerProducts>>(cartJsonData);
                if (cartCustomerProducts != null)
                {
                    List<ProductsIds>? ProductIds = new List<ProductsIds>();

                    foreach (var item in cartCustomerProducts)
                    {
                        var rowData = new ProductsIds();
                        rowData.ProductId = item.ProductId;
                        ProductIds.Add(rowData);
                    }

                    string ProductIdsJson = JsonConvert.SerializeObject(ProductIds);

                    //-- get products list by ids
                    Dictionary<string, object>? requestParametersAllProducts = new Dictionary<string, object>();
                    requestParametersAllProducts.Add("ProductsIds", ProductIdsJson);
                    var ApiConfigurationForGetAllProducts = await this._apiOperationServicesDAL.GetAPIConfiguration("get-products-list-by-ids");
                    string? allProductsDataJson = "[]";
                    if (ApiConfigurationForGetAllProducts != null)
                    {
                        allProductsDataJson = await this._apiOperationServicesDAL.GetApiData(requestParametersAllProducts, ApiConfigurationForGetAllProducts);

                    }

                    //--Calcualte Discount for products
                    string productsAfterDiscount = await _calculationHelper.CalculateDiscountsForProducts((allProductsDataJson ?? "[]"));
                    

                    var CartItems = JsonConvert.DeserializeObject<List<ApiProductEntity?>>(productsAfterDiscount ?? "[]");

                    if (CartItems != null)
                    {
                        bool IsDiscountExecuted = false;
                        Dictionary<string, object> apiResponse = new Dictionary<string, object>();
                        foreach (var item in CartItems)
                        {
                            //--If discount is applied from the coupon to a product then do not execute again for each product
                            if (item.IsDiscountAllowed == true && IsDiscountExecuted == false)
                            {
                                var couponDiscount = await _calculationHelper.CalculateCouponDiscountValueForProduct(item.ProductId, item.Price, CouponCode, cartJsonData);
                                if (couponDiscount != null)
                                {
                                    item.Quantity = Convert.ToInt32(cartCustomerProducts?.FirstOrDefault(x => x.ProductId == item.ProductId)?.Quantity);
                                    decimal DiscountValueAfterCouponApplied = Convert.ToDecimal(couponDiscount["DiscountValueAfterCouponApplied"].ToString());
                                    apiResponse.Add("DiscountValueAfterCouponApplied", DiscountValueAfterCouponApplied);
                                    apiResponse.Add("DiscountId", Convert.ToInt32(couponDiscount["DiscountId"].ToString()));
                                   

                                    if (Convert.ToInt32(couponDiscount["DiscountTypeId"].ToString()) == (short)DiscountTypesEnum.AppliedOnOrderTotal)
                                    {
                                        apiResponse.Add("DiscountValueAfterCouponAppliedWithQuantity", (DiscountValueAfterCouponApplied));
                                    }
                                    else
                                    {
                                        apiResponse.Add("DiscountValueAfterCouponAppliedWithQuantity", (DiscountValueAfterCouponApplied * item.Quantity));
                                    }
                                    //--set the flag to true
                                    IsDiscountExecuted = DiscountValueAfterCouponApplied > 0 ? true : false;
                                }
                            }



                        }

                        #region result
                        result.Data = JsonConvert.SerializeObject(apiResponse);
                        result.StatusCode = 200;
                        result.StatusMessage = "Ok";
                        result.ErrorMessage = String.Empty;
                        apiActionResult = new APIActionResult(result);
                        #endregion

                    }
                    else
                    {
                        #region result
                        result.Data = "[]";
                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "No cart itme selected!";
                        apiActionResult = new APIActionResult(result);
                        #endregion

                    }
                }
                else
                {
                    #region result
                    result.Data = "[]";
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "No cart itme selected!";
                    apiActionResult = new APIActionResult(result);
                    #endregion
                }

                #endregion



            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }





            return apiActionResult;


        }

        [Route("localization-cstm-portal/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetLocalizationControlsJsonDataCstmPortal(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {

                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }

                }


                #region new
                int EntityId = 0;
                string langCode = string.Empty;
                if (requestParameters!=null)
                {
                     EntityId = Convert.ToInt32(requestParameters["entityId"].ToString() ?? "0");
                    langCode = requestParameters["languageCode"].ToString() ?? "en";
                }

                ScrnsLocalizationEntity scrnsLocalization = new ScrnsLocalizationEntity()
                {
                    ScreenId = EntityId,
                    AppModuleId = (short)AppModulesEnum.CustomerPortal,
                    LanguageId = CommonConversionHelper.GetLanguageIdbyLanguageCode(langCode)
                };
                var resultLocalization = await _commonServicesDAL.GetScreenLocalizationJsonDataDAL(scrnsLocalization);


                if (resultLocalization != null && !String.IsNullOrWhiteSpace(resultLocalization.LabelsJsonData))
                {
                    #region result
                    result.Data = resultLocalization.LabelsJsonData;
                    result.StatusCode = 200;
                    result.StatusMessage = "Ok";
                    result.ErrorMessage = String.Empty;
                    apiActionResult = new APIActionResult(result);
                    #endregion
                }
                else
                {
                  
                    #region result
                    result.Data = "[]";
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "No cart itme selected!";
                    apiActionResult = new APIActionResult(result);
                    #endregion

                }

                #endregion



            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }





            return apiActionResult;


        }

        [Route("en-ur-drow-pass-rndom/{UrlName?}")]//--For security reason, just keep url not readable
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> EncryptPassword(string UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {

                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param != null && param.Count != 0)
                {

                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);

                        }

                    }

                }


                #region new
                string Password = String.Empty;
                if (requestParameters!=null && requestParameters.ContainsKey("Password"))
                {
                     Password = requestParameters["Password"].ToString() ?? "";
                     Password = CommonConversionHelper.Encrypt(Password);
                }
              
                Dictionary<string, string> responseDic= new Dictionary<string, string>();
                responseDic.Add("Password", Password);

                #region result
                result.Data = JsonConvert.SerializeObject(responseDic);
                result.StatusCode = 200;
                result.StatusMessage = "Ok";
                result.ErrorMessage = String.Empty;
                apiActionResult = new APIActionResult(result);
                #endregion


                #endregion



            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }





            return apiActionResult;


        }



        [Route("reset-password-by-phone/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> ResetPasswordByPhone(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {
                if (param != null && param.Count != 0)
                {
                    Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);
                        }
                    }

                    string? PhoneNumber = requestParameters != null ? requestParameters["PhoneNumber"].ToString() : "";
                    string? NewPassword = requestParameters != null ? requestParameters["NewPassword"].ToString() : "";
                    string? Email = requestParameters != null && requestParameters.ContainsKey("Email") ? requestParameters["Email"].ToString() : "";

                    if (String.IsNullOrEmpty(PhoneNumber))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please provide phone number!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    if (String.IsNullOrEmpty(NewPassword))
                    {
                        result.StatusCode = 204;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please provide new password!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    // Encrypt the password
                    string encryptedPassword = CommonConversionHelper.Encrypt(NewPassword);

                    // Reset password using phone number
                    string PasswordResetResponse = await this._userManagementServicesDAL.ResetUserPasswordByPhoneDAL(PhoneNumber, encryptedPassword);

                    if (PasswordResetResponse == "Saved Successfully!")
                    {
                        #region result
                        result.Data = "[]";
                        result.StatusCode = 200;
                        result.StatusMessage = "Ok";
                        result.Message = "Password reset successfully";
                        result.ErrorMessage = String.Empty;
                        apiActionResult = new APIActionResult(result);

                        #endregion
                    }
                    else
                    {
                        result.StatusCode = 501;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Failed to reset password. Please try again!";
                        apiActionResult = new APIActionResult(result);
                    }
                }
                else
                {
                    result.StatusCode = 501;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "An error is occured while processing your request.";
                    apiActionResult = new APIActionResult(result);
                }
            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error is occured while processing your request.";
                apiActionResult = new APIActionResult(result);
            }

            return apiActionResult;
        }

        [Route("reset-password-firebase/{UrlName?}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> ResetPasswordFirebase(string? UrlName, [FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            //--Api result type declared in resultType variable
            string resultType = "json";

            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;

            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);

            //--This data variable will be used for storing data
            string? data = string.Empty;
            #endregion

            try
            {
                if (param != null && param.Count != 0)
                {
                    Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                    if (param.ContainsKey("requestParameters"))
                    {
                        string? ParamKeyValue = param["requestParameters"].ToString();
                        if (!String.IsNullOrWhiteSpace(ParamKeyValue))
                        {
                            requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(ParamKeyValue);
                        }
                    }

                    string? Email = requestParameters != null && requestParameters.ContainsKey("Email") ? requestParameters["Email"].ToString() : "";
                    string? PhoneNumber = requestParameters != null && requestParameters.ContainsKey("PhoneNumber") ? requestParameters["PhoneNumber"].ToString() : "";
                    string? NewPassword = requestParameters != null && requestParameters.ContainsKey("NewPassword") ? requestParameters["NewPassword"].ToString() : "";
                    string? FirebaseUid = requestParameters != null && requestParameters.ContainsKey("FirebaseUid") ? requestParameters["FirebaseUid"].ToString() : "";

                    // Validate required fields
                    if (String.IsNullOrEmpty(Email) && String.IsNullOrEmpty(PhoneNumber))
                    {
                        result.StatusCode = 400;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please provide either email or phone number!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    if (String.IsNullOrEmpty(NewPassword))
                    {
                        result.StatusCode = 400;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Please provide new password!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    if (String.IsNullOrEmpty(FirebaseUid))
                    {
                        result.StatusCode = 400;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Firebase authentication required!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    // Validate password strength
                    if (NewPassword.Length < 6)
                    {
                        result.StatusCode = 400;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "Password must be at least 6 characters long!";
                        apiActionResult = new APIActionResult(result);
                        return apiActionResult;
                    }

                    // This endpoint uses Firebase Authentication for password reset
                    // The actual password update is handled by Firebase Auth
                    // We just validate the request and return success
                    // The frontend should handle the Firebase password update

                    try
                    {
                        // Log the password reset attempt for audit purposes
                        string logMessage = $"Firebase password reset attempted for: {(!String.IsNullOrEmpty(Email) ? Email : PhoneNumber)}";
                        await this._commonServicesDAL.LogRunTimeExceptionDAL(logMessage, "Password Reset", "Firebase Auth");

                        // In a real implementation, you might want to:
                        // 1. Verify the Firebase UID is valid
                        // 2. Update any local user data if needed
                        // 3. Send confirmation email/SMS

                        #region result
                        result.Data = "[]";
                        result.StatusCode = 200;
                        result.StatusMessage = "Ok";
                        result.Message = "Password reset request processed successfully. Please complete the reset using Firebase Authentication.";
                        result.ErrorMessage = String.Empty;
                        apiActionResult = new APIActionResult(result);
                        #endregion
                    }
                    catch (Exception resetEx)
                    {
                        await this._commonServicesDAL.LogRunTimeExceptionDAL(resetEx.Message, resetEx.StackTrace, resetEx.StackTrace);
                        
                        result.StatusCode = 500;
                        result.StatusMessage = "Error";
                        result.ErrorMessage = "An error occurred while processing the password reset request.";
                        apiActionResult = new APIActionResult(result);
                    }
                }
                else
                {
                    result.StatusCode = 400;
                    result.StatusMessage = "Error";
                    result.ErrorMessage = "Invalid request parameters.";
                    apiActionResult = new APIActionResult(result);
                }
            }
            catch (Exception ex)
            {
                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 500;
                result.StatusMessage = "Error";
                result.ErrorMessage = "An error occurred while processing your request.";
                apiActionResult = new APIActionResult(result);
            }

            return apiActionResult;
        }

        [Route("download-digital-file/{order_item_id}/{user_id}")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        [HttpGet]
        public async Task<IActionResult> DownloadDigitalFile(int order_item_id, int user_id)
        {
          

            try
            {
                var digitalOrderInfo = await this._salesServicesDAL.GetDigitalOrderInfoForCustomerByIdDAL(order_item_id, user_id);
                if (digitalOrderInfo!=null && digitalOrderInfo.IsDigitalProduct==true && !String.IsNullOrWhiteSpace(digitalOrderInfo.DigitalFileDownloadUrl))
                {

                    if (digitalOrderInfo.DigitalFileDownloadUrl.StartsWith("https://") || digitalOrderInfo.DigitalFileDownloadUrl.StartsWith("http://"))
                    {

                        string path = digitalOrderInfo.DigitalFileDownloadUrl;
                        string fileName = Path.GetFileName(path);
                        string fileExtension = Path.GetExtension(path);
                      
                        string contentType = await this._filesHelpers.GetFileContentTypeForFileExtension(fileExtension);


                        byte[]? fileBytes = null;
                        using (var httpClient = new HttpClient())
                        {
                            var response = await httpClient.GetAsync(path);
                            if (response.IsSuccessStatusCode)
                            {
                                fileBytes = await response.Content.ReadAsByteArrayAsync();
                            }
                            else
                            {
                                throw new Exception("Failed to download file.");
                            }
                        }

                        var fileStream = new MemoryStream(fileBytes);
                        

                        return File(fileStream, contentType ?? "application/octet-stream", fileName);



                    }
                    else
                    {
                        string path = System.IO.Path.Combine(Directory.GetCurrentDirectory(), "wwwroot" + digitalOrderInfo.DigitalFileDownloadUrl);
                        string fileName = Path.GetFileName(path);
                        string fileExtension = Path.GetExtension(path);
                        var file = System.IO.File.ReadAllBytes(path);
                        var fileStream = new MemoryStream(file);

                        string contentType = await this._filesHelpers.GetFileContentTypeForFileExtension(fileExtension);


                        return File(fileStream, contentType ?? "application/octet-stream", fileName);


                    }


                }
                else
                {

                    return StatusCode(StatusCodes.Status404NotFound);
                }

             


            }
            catch (Exception ex)
            {

                #region log error
                await this._commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                return StatusCode(StatusCodes.Status500InternalServerError);
            }


        }


    }
}
